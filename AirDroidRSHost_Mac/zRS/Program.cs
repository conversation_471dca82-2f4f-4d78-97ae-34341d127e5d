﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using System.Threading;
using System.Security.Cryptography;
using System.Drawing;
using System.Collections.Specialized;
using System.Xml;
using System.Diagnostics;
using System.Management;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using System.ServiceProcess;

using iTong.CoreModule;
using iTong.CoreFoundation;
using iTong.Device;
using iTong.Android;

using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;

using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;

using skContextMenuStrip = iTong.CoreModule.skMenu;
using ToolStripMenuItem = iTong.CoreModule.skMenuItem;
using ToolStripItem = iTong.CoreModule.skMenuItem;
using PictureBox = iTong.CoreModule.skPictureBox;
using Label = iTong.CoreModule.skLabel;
using NotifyIcon = iTong.CoreModule.skStatusItem;
using ContentAlignment = iTong.CoreModule.ContentAlignment;

namespace iTong.CoreModule
{
    public static class Program
    {
       

        public static int Main(string[] args)
        {
            int iResult = 1;
            string[] arrPara = null;
            string cmd = string.Empty;
            bool cmdIsEmpty = false;

            try
            {                                
                if (args != null && args.Length > 0)
                {
                    if (args[0] == ParaMgr.CommandRunProxy)
                    {
                        //需要等待用户会话创建成功，再初调用NSApplication.Init()                
                        if (!MiniAPI.WaitForWindowSession())
                        {
                            MiniAPI.WriteLine("WaitForWindowSession failed.");
                            iResult = 0;
                            goto DoExit;
                        }
                    }
                    else if (args[0] == ParaMgr.CommandRunVideo)
                    {
                        //启动计时器检测屏幕是否休眠
                        MiniAPI.WakeUpDisplayWithTimer();
                    }
                }

                NSApplication.Init();

                //ParaMgr.Arguments = new string[1] { ParaMgr.CommandRunWidnows };
                //ParaMgr.Arguments = new string[1] { ParaMgr.CommandRunVideo };
                //ParaMgr.Arguments = new string[1] { ParaMgr.CommandRunSafeMode };
                //ParaMgr.Arguments = new string[1] { ParaMgr.CommandRunServiceDebug };
                //ParaMgr.Arguments = new string[1] { ParaMgr.CommandRunService };
                //ParaMgr.Arguments = new string[1] { ParaMgr.CommandInstallService };
                //ParaMgr.Arguments = new string[1] { ParaMgr.CommandRunScreen };
                //ParaMgr.Arguments = new string[1] { ParaMgr.CommandRunProxy };

#if DEBUG
                //ParaMgr.Arguments = new string[2] { ParaMgr.CommandRunWidnows, ParaMgr.CommandUserRun };
#endif

                //SocketMgr.KillCurrentApp(false);

                arrPara = ParaMgr.Arguments;               

                Console.WriteLine(Folder.AppFile);

                //Common.Chmod("/Library/Caches/AirDroidRemoteSupport", 777);
                //Common.Chown("/Library/Caches/AirDroidRemoteSupport");
                //ServiceMgr.GetConsoleUserName();
                //ServiceMgr.LaunchApp(ServiceMgr.ServiceNameForRS_Windows, Application.ExecutablePath, ParaMgr.CommandRunWidnows);


                if (arrPara == null || arrPara.Length == 0)
                {
                    cmdIsEmpty = true;
                    
                    arrPara = new string[] { ParaMgr.CommandInstallService };
                    ParaMgr.LogPara(arrPara[0], arrPara);

                    ///双击、安装的情况下 rs无需最小化。
                    SettingMgr.SetValue(KeyNameForRS.HideWindow, false);

                    string windowTitle = string.Empty;
                    string svrName = string.Empty;

#if BIZ
                    windowTitle = ServiceMgr.ServiceNameForBiz;
                    svrName = ServiceMgr.ServiceNameForBiz;
#else
                    windowTitle = ServiceMgr.ServiceNameForRS_Session;
                    svrName = ServiceMgr.ServiceNameForRS_Session;
#endif

                    //用于唤醒窗口
                    MyAPI.SendMessage(windowTitle);

                    //获取服务状态
                    ServiceControllerStatus status = ServiceMgr.GetServiceStatus(svrName, Application.ExecutablePath);

                    if (status != ServiceControllerStatus.None)
                    {
                        //以服务方式运行、或通过指令判断runService进程存在
                        if (status == ServiceControllerStatus.Running || ServiceMgr.CheckCurrentProcessExist(ParaMgr.CommandRunService) || Application.ExecutablePath.StartsWith("/Applications/"))
                        {
                            //不响应DPI缩放
                            //MyRegistry.SetDpiAwareness(DpiAwareness.unaware);

                            arrPara = new string[] { ParaMgr.CommandRunWidnows };
                        }
                        else
                        {
                            arrPara = new string[] { ParaMgr.CommandRunService };
                        }
                    }

                    //cmdIsEmpty = false;
                    //arrPara = new string[] { ParaMgr.CommandRunService };

                    //更新启动参数
                    ParaMgr.Arguments = arrPara;
                }

                cmd = arrPara[0];

                ParaMgr.LogPara(arrPara[0], arrPara);

                string cmdArgs = cmdIsEmpty ? ParaMgr.CommandPSN : cmd;

                //判断进程是否已经存在
                if (ServiceMgr.GetCurrentAppProcessPID(cmdArgs) > 0)
                {
                    if (cmdArgs == ParaMgr.CommandPSN)
                    {
                        //杀掉用户手动双击运行的进程
                        ServiceMgr.KillCurrentAppContainKey(cmdArgs);
                    }
                    else
                    {
                        goto DoReturn;
                    }
                }

                switch (cmd)
                {
                    case ParaMgr.CommandRunWidnows:
                        {
                            if (cmdIsEmpty)
                            {
                                //以命令行方法运行CommandRunWidnows，这样APP是以当前用户身份
                                //增加一个参数CommandUserRun，用户判断是否开机自启动
                                Common.RunApp(Application.ExecutablePath, ParaMgr.CommandRunWidnows, ParaMgr.CommandUserRun);
                                goto DoReturn;
                            }
                            break;
                        }

                    case ParaMgr.CommandRunService:
                        {
                            if (cmdIsEmpty)
                            {
                                //以服务方式运行CommandRunService
                                ServiceMgr.LaunchApp(ServiceMgr.ServiceNameForRS_Session, Application.ExecutablePath, ParaMgr.CommandRunService);
                                goto DoReturn;
                            }
                            else
                            {
                                if (ServiceMgr.IsRoot)
                                {
                                    //将ApplicationDataFolder设置为当前用户所有权，避免没有读写权限
                                    ServiceMgr.Chown(Folder.ApplicationDataFolder);
                                }

                                //用户已经登录
                                if (ServiceMgr.IsUserLogin() && !Application.ExecutablePath.Contains("bin/Debug"))
                                {
                                    //以服务方式运行CommandRunWidnows
                                    ServiceMgr.LaunchApp(ServiceMgr.ServiceNameForRS_Windows, Application.ExecutablePath, ParaMgr.CommandRunWidnows);
                                }
                            }

                            break;
                        }
                }                

                MyLog.WriteLine("RsAPI.Ini start");

                //避免调用卸载服务需要初始化花费时间
                if (cmd.Contains("t=run"))
                {
#if BIZ
                    BizAPI.Init();
#else
                    RsAPI.Init();
#endif
                }

#if DEBUG
                if (RunTest())
                    goto DoExit;
#endif

                MyLog.WriteLine("ServiceMgr.Script.Clear");

                //RsAPI.Init();
                //skDevice_Detail_Info info = MyRS.CreateDeviceDetailInfo();
                //string msgJson = MyJson.SerializeToJsonStringAsFormat(info);
                //Console.WriteLine(msgJson);

                ServiceMgr.Script.Clear();
                switch (cmd)
                {

                    case ParaMgr.CommandRunSafeMode:
                        ShowSafeMode(arrPara);
                        break;

                    case ParaMgr.CommandRunWidnows:
                        ShowWindow(arrPara, cmdIsEmpty);
                        break;

                    case ParaMgr.CommandRunUser:
                        arrPara[0] = ParaMgr.CommandRunWidnows;
                        Common.CallCmdExe(arrPara, 0, Application.ExecutablePath, false, true);
                        break;

                    case ParaMgr.CommandRunService:
                        ServiceRS srv = new ServiceRS();    //运行RS服务
                        ServiceBase.Run(srv);
                        break;

                    case ParaMgr.CommandRunServiceDebug:
                        ParaMgr.RunServiceDebug(true);
                        break;

                    case ParaMgr.CommandRunVideo:
                        ParaMgr.RunVideo();
                        break;

                    case ParaMgr.CommandRunProxy:
                        ParaMgr.RunProxy();
                        break;

                    case ParaMgr.CommandRunChat:
                        PageHelper.InitCef();
                        //Application.Run(new frmChat(UserBase.LocalDevice, PageHelper.PathRSChat, false));
                        break;

                    case ParaMgr.CommandRunTask:
                        break;

                    case ParaMgr.CommandUninstallService:
                        ServiceMgr.KillCurrentExistApp();

                        ServiceMgr.ServiceUninstall(ServiceMgr.ServiceNameForRS_Service, true);     //卸载RS服务
                        ServiceMgr.ServiceUninstall(ServiceMgr.ServiceNameForRS_Session, true);     //卸载RS_Session服务
                        ServiceMgr.ServiceUninstall(ServiceMgr.ServiceNameForRS_Windows);           //卸载RS_Windows服务
                        ServiceMgr.ServiceUninstall(ServiceMgr.ServiceNameForRS_SafeMode);          //卸载RS_SafeMode服务
                        ServiceMgr.ServiceUninstall(ServiceMgr.ServiceNameForRS_Video);             //卸载RS_Video服务
                        ServiceMgr.ServiceUninstall(ServiceMgr.ServiceNameForRS_Proxy);             //卸载RS_Proxy服务

                        //ServiceMgr.ScriptAppendKillSelf();
                        ServiceMgr.RunAppleScript(ServiceMgr.Script.ToString());
                        break;

                    case ParaMgr.CommandInstallService:
                        ServiceMgr.KillCurrentExistApp();

                        ServiceMgr.ServiceInstall(ServiceMgr.ServiceNameForRS_Proxy, ServiceMgr.ServicePath, StartType.delayed_auto, ServiceMgr.CreatePlistAsProxy());       //安装RS_Proxy服务
                        ServiceMgr.ServiceInstall(ServiceMgr.ServiceNameForRS_Video, ServiceMgr.ServicePath, StartType.delayed_auto, ServiceMgr.CreatePlistAsVideo());       //安装RS_Video服务
                        ServiceMgr.ServiceInstall(ServiceMgr.ServiceNameForRS_Windows, ServiceMgr.ServicePath, StartType.delayed_auto, ServiceMgr.CreatePlistAsWindows());   //安装RS_Windows服务
                        ServiceMgr.ServiceInstall(ServiceMgr.ServiceNameForRS_SafeMode, ServiceMgr.ServicePath, StartType.delayed_auto, ServiceMgr.CreatePlistAsSafeMode()); //安装RS_SafeMode服务
                        ServiceMgr.ServiceInstall(ServiceMgr.ServiceNameForRS_Session, ServiceMgr.ServicePath, StartType.auto, ServiceMgr.CreatePlistAsSession(), true);     //安装RS_Session服务
                        ServiceMgr.ServiceInstall(ServiceMgr.ServiceNameForRS_Service, ServiceMgr.ServicePath, StartType.auto, ServiceMgr.CreatePlistAsService(), true);     //安装RS服务

                        //ServiceMgr.ScriptAppendKillSelf();
                        ServiceMgr.ScriptAppendCreateServiceDll();

                        Console.WriteLine(ServiceMgr.Script.ToString());

                        ServiceMgr.RunAppleScript(ServiceMgr.Script.ToString());
                        break;
                }
            }
            catch (Exception ex)
            {
                MyLog.WriteLine(ex.Message);
            }

        DoExit:
            ServiceMgr.WriteLine("Main.DoExit -> " + string.Join(" ", arrPara));


        DoReturn:
            Application.Exit();

            return iResult;
        }

        private static void ExitApp()
        {   
            try
            {
                Application.Exit();
            }
            catch (Exception ex)
            {
                ServiceMgr.WriteLine("Application.Exit Error -> " + ex.ToString());
            }

            ServiceMgr.ScriptClear();
            ServiceMgr.ScriptAppendKillSelf();
            ServiceMgr.RunAppleScript(ServiceMgr.Script.ToString());
        }

        private static bool RunTest()
        {
            //skDevice dev = new skDevice();
            //dev.Resolution = new Size(300, 100);

            //string strJson = MyJson.SerializeToJsonString(dev);

            //ServiceMgr.CheckCurrentUserIsAdmin();

            //string strFileName = "/Library/Caches/AirDroidRemoteSupport/Setting.dat";
            //skFileUploadRequest uploadRequest = FileUploadTask.CreateFileUploadRequest(skFileUploadBucket.RS, strFileName, 0, "US");
            //FileUploadTask uploadTask = FileUploadMgr.Instance.NewTask(strFileName, uploadRequest, true, true);

            //PowerMgr.RegisterPowerNotifications();
            //PowerMgr.UnregisterPowerNotifications();

            //bool blnValue = InputSender.GetCapsLockState();

            //InputSender.SetCapsLockState(!blnValue);

            //bool blnValueNew = InputSender.GetCapsLockState();



            return false;    
        }

        private static void ShowWindow(string[] arrPara, bool cmdIsEmpty = false)
        {
            SocketMgr.LogApp(string.Format("ShowWindow -> cmdIsEmpty = {0}, ServiceMgr.IsRoot = {1}, arrPara.Length = {2}, arrPara[0] = {3}, AutoStart = {4}", cmdIsEmpty, ServiceMgr.IsRoot, arrPara.Length, arrPara[0], SettingMgr.GetValue<bool>(SettingKey.AutoStart, true)));

            //非双击启动 且 非开机自启动 直接return
            if (!cmdIsEmpty && arrPara.Length == 1 && !SettingMgr.GetValue<bool>(SettingKey.AutoStart, true))
                return;

            ////用户双击启动
            //if (cmdIsEmpty)
            //{
            //    //由LaunchAgents启动，参数只有一个且是CommandRunWidnows，判断是否启动显示UI窗体
            //    if (arrPara.Length == 1 && arrPara[0] == ParaMgr.CommandRunWidnows)
            //    {
            //        if (!SettingMgr.GetValue<bool>(SettingKey.AutoStart, true))
            //        {
            //            //设置开机不自启动
            //            return;
            //        }
            //    }
            //}

            if (!ServiceMgr.IsRoot)
            {
                MyLog.WriteLine("SetActivationPolicy start");

                if (arrPara[0] == ParaMgr.CommandRunWidnows)
                {
                    //应用程序在 Dock 中显示，并且可以有菜单栏。通常用于标准 GUI 应用程序。
                    Application.SetActivationPolicy(NSApplicationActivationPolicy.Regular);

                }
                //else
                //{
                //    // 应用程序既不在 Dock 中显示，也没有菜单栏。适用于纯后台应用程序。
                //    Application.SetActivationPolicy(NSApplicationActivationPolicy.Prohibited);

                //    //应用程序不在 Dock 中显示，但可以有菜单栏。适用于辅助应用程序或工具。
                //    //NSApplicationActivationPolicy.Accessory
                //}
            }

            //创建Service.dll
            ParaMgr.CreateOrDeleteServiceDll(true);

            //启动界面程序
            NSApplication.Main(arrPara);
        }

        private static void ShowSafeMode(string[] arrPara)
        {
            SocketMgr.RsCallback += frmSafeMode.OnRsController_Callback;

            if (!ParaMgr.RunSafeMode())
                return;

            ShowWindow(arrPara);
        }
    }
}
