Last login: Fri Aug  1 13:13:37 on ttys000
TB-Mac-099deMac-mini10123:~ ceshi$ chmod +x /Users/<USER>/Desktop/rs/fix_plist_bom.sh 
TB-Mac-099deMac-mini10123:~ ceshi$ sudo /Users/<USER>/Desktop/rs/fix_plist_bom.sh 
Password:
Sorry, try again.
Password:
=== 修复plist文件的BOM问题 ===
修复时间: 2025年 8月 1日 星期五 13时38分23秒 CST

1. 检查当前文件编码...
Session plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: XML 1.0 document text, UTF-8 Unicode (with BOM) text
Service plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist: XML 1.0 document text, UTF-8 Unicode (with BOM) text, with very long lines

2. 备份原始文件...
✅ 备份完成

3. 移除BOM并重新保存文件...
处理session plist...
处理service plist...
✅ BOM移除完成

4. 验证修复后的文件编码...
Session plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: XML 1.0 document text, UTF-8 Unicode (with BOM) text
Service plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist: XML 1.0 document text, UTF-8 Unicode (with BOM) text, with very long lines

5. 验证plist格式...
验证session plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: OK
验证service plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist: OK

6. 卸载现有服务...
✅ 服务卸载完成

7. 重新加载修复后的服务...
加载session plist...
✅ Session plist加载成功
输出: /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: Invalid property list

加载service plist...
✅ Service plist加载成功
输出: /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist: Invalid property list

8. 检查服务状态...
当前注册的remotesupport服务:

当前运行的RemoteSupport进程:

=== 修复完成 ===

如果还有问题，可以使用以下命令恢复备份:
sudo cp /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist.backup /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
sudo cp /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist.backup /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist
TB-Mac-099deMac-mini10123:~ ceshi$ 
