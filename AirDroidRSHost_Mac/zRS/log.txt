2025-08-01 09:15:38.8457 Command -> t=installService	Pid=65645
CapsLock -> False
AppDomain.CurrentDomain.BaseDirectory -> /Applications/RemoteSupport.app/Contents/MonoBundle/
Application.ExecutablePath -> /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
Application.StartupPath -> /Applications/RemoteSupport.app
Environment.CurrentDirectory -> /Applications/RemoteSupport.app/Contents/Resources
Folder.AppFolder -> /Applications/RemoteSupport.app/Contents/MonoBundle/

2025-08-01 09:15:38.8863 [ServiceMgr.static] 当前用户：root
2025-08-01 09:15:38.8866 [ServiceMgr.static] IsRoot=True
2025-08-01 09:15:38.8872 [ServiceMgr.static] macOS版本：10.12.6
2025-08-01 09:15:38.8921 [ServiceMgr.static] 已设置日志文件夹权限
2025-08-01 09:15:38.8927 [Program.Main] 开始检查服务状态，svrName=com.sandstudio.remotesupport.session
2025-08-01 09:15:38.8941 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.session, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:15:39.2171 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:15:39.2173 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:15:39.2176 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:39.2180 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:15:39.2182 [Program.Main] 服务状态检查结果：status=None
2025-08-01 09:15:39.2184 [Program.Main] 服务状态为None，保持当前参数
2025-08-01 09:15:39.2186 [Program.Main] 最终执行命令：t=installService
2025-08-01 09:15:39.2191 Command -> t=installService	Pid=65645
CapsLock -> False
AppDomain.CurrentDomain.BaseDirectory -> /Applications/RemoteSupport.app/Contents/MonoBundle/
Application.ExecutablePath -> /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
Application.StartupPath -> /Applications/RemoteSupport.app
Environment.CurrentDirectory -> /Applications/RemoteSupport.app/Contents/Resources
Folder.AppFolder -> /Applications/RemoteSupport.app/Contents/MonoBundle/

2025-08-01 09:15:39.2194 [Program.Main] 进程检查参数：cmdArgs=psn_, cmdIsEmpty=True
2025-08-01 09:15:39.2543 [Program.Main] 现有进程检查：existingPID=0
2025-08-01 09:15:39.2545 RsAPI.Ini start
2025-08-01 09:15:39.2549 ServiceMgr.Script.Clear
2025-08-01 09:15:39.2554 [Program.Main] 开始安装服务
2025-08-01 09:15:39.2821 ScriptAppendKillPid 65644 Begin
2025-08-01 09:15:39.2823 [Program.Main] 安装RS_Proxy服务
2025-08-01 09:15:39.2873 [ServiceMgr.CreatePlist] macOS版本：10.12.6, isOldMacOS=True
2025-08-01 09:15:39.2875 [ServiceMgr.CreatePlist] 跳过AssociatedBundleIdentifiers（旧版macOS兼容）
2025-08-01 09:15:39.2878 [ServiceMgr.CreatePlist] 强制验证：dictPlist包含AssociatedBundleIdentifiers=False
2025-08-01 09:15:39.2989 [ServiceMgr.CreatePlist] 生成的plist内容长度：658
2025-08-01 09:15:39.2991 [ServiceMgr.CreatePlist] 确认：生成的plist不包含AssociatedBundleIdentifiers
2025-08-01 09:15:39.3221 [ServiceMgr.CreatePlist] 创建临时plist文件：/tmp/AirDroidRemoteSupport/ed71f92e.plist
2025-08-01 09:15:39.3230 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.proxy, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:15:39.3702 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:15:39.3704 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:15:39.3972 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:39.3974 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:15:39.3989 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.proxy, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:15:39.4461 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:15:39.4465 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:15:39.4469 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:39.4474 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:15:39.4476 [Program.Main] 安装RS_Video服务
2025-08-01 09:15:39.4923 [ServiceMgr.CreatePlist] macOS版本：10.12.6, isOldMacOS=True
2025-08-01 09:15:39.4925 [ServiceMgr.CreatePlist] 跳过AssociatedBundleIdentifiers（旧版macOS兼容）
2025-08-01 09:15:39.4927 [ServiceMgr.CreatePlist] 强制验证：dictPlist包含AssociatedBundleIdentifiers=False
2025-08-01 09:15:39.4930 [ServiceMgr.CreatePlist] 生成的plist内容长度：683
2025-08-01 09:15:39.4932 [ServiceMgr.CreatePlist] 确认：生成的plist不包含AssociatedBundleIdentifiers
2025-08-01 09:15:39.5107 [ServiceMgr.CreatePlist] 创建临时plist文件：/tmp/AirDroidRemoteSupport/eb5dc0be.plist
2025-08-01 09:15:39.5110 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.video, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:15:39.5576 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:15:39.5579 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:15:39.5581 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:39.5583 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:15:39.5585 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.video, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:15:39.6076 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:15:39.6079 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:15:39.6082 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:39.6083 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:15:39.6085 [Program.Main] 安装RS_Windows服务
2025-08-01 09:15:39.6094 [ServiceMgr.CreatePlist] macOS版本：10.12.6, isOldMacOS=True
2025-08-01 09:15:39.6096 [ServiceMgr.CreatePlist] 跳过AssociatedBundleIdentifiers（旧版macOS兼容）
2025-08-01 09:15:39.6098 [ServiceMgr.CreatePlist] 强制验证：dictPlist包含AssociatedBundleIdentifiers=False
2025-08-01 09:15:39.6103 [ServiceMgr.CreatePlist] 生成的plist内容长度：687
2025-08-01 09:15:39.6104 [ServiceMgr.CreatePlist] 确认：生成的plist不包含AssociatedBundleIdentifiers
2025-08-01 09:15:39.6112 [ServiceMgr.CreatePlist] 创建临时plist文件：/tmp/AirDroidRemoteSupport/d7ec0251.plist
2025-08-01 09:15:39.6114 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.windows, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:15:39.6689 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:15:39.6692 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:15:39.6694 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:39.6695 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:15:39.6697 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.windows, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:15:39.7162 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:15:39.7165 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:15:39.7168 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:39.7169 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:15:39.7171 [Program.Main] 安装RS_SafeMode服务
2025-08-01 09:15:39.7182 [ServiceMgr.CreatePlist] macOS版本：10.12.6, isOldMacOS=True
2025-08-01 09:15:39.7184 [ServiceMgr.CreatePlist] 跳过AssociatedBundleIdentifiers（旧版macOS兼容）
2025-08-01 09:15:39.7186 [ServiceMgr.CreatePlist] 强制验证：dictPlist包含AssociatedBundleIdentifiers=False
2025-08-01 09:15:39.7190 [ServiceMgr.CreatePlist] 生成的plist内容长度：689
2025-08-01 09:15:39.7192 [ServiceMgr.CreatePlist] 确认：生成的plist不包含AssociatedBundleIdentifiers
2025-08-01 09:15:39.7370 [ServiceMgr.CreatePlist] 创建临时plist文件：/tmp/AirDroidRemoteSupport/5e25cec9.plist
2025-08-01 09:15:39.7373 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.safemode, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:15:39.7842 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:15:39.7846 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:15:39.7848 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:39.7849 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:15:39.7851 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.safemode, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:15:39.8327 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:15:39.8331 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:15:39.8333 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:39.8335 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:15:39.8337 [Program.Main] 安装RS_Session服务
2025-08-01 09:15:39.8350 [ServiceMgr.CreatePlistAsSession] 开始创建Session plist
2025-08-01 09:15:39.8353 [ServiceMgr.CreatePlistAsSession] 使用可执行文件路径：/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:39.8356 [ServiceMgr.CreatePlist] macOS版本：10.12.6, isOldMacOS=True
2025-08-01 09:15:39.8357 [ServiceMgr.CreatePlist] 跳过AssociatedBundleIdentifiers（旧版macOS兼容）
2025-08-01 09:15:39.8359 [ServiceMgr.CreatePlist] 强制验证：dictPlist包含AssociatedBundleIdentifiers=False
2025-08-01 09:15:39.8362 [ServiceMgr.CreatePlist] 生成的plist内容长度：608
2025-08-01 09:15:39.8364 [ServiceMgr.CreatePlist] 确认：生成的plist不包含AssociatedBundleIdentifiers
2025-08-01 09:15:39.8368 [ServiceMgr.CreatePlist] 创建临时plist文件：/tmp/AirDroidRemoteSupport/075685a5.plist
2025-08-01 09:15:39.8370 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.session, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:15:39.8829 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:15:39.8831 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:15:39.8833 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:39.8836 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:15:39.8996 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.session, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:15:39.9452 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:15:39.9454 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:15:39.9457 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:39.9459 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:15:39.9461 [Program.Main] 安装RS服务
2025-08-01 09:15:39.9473 [ServiceMgr.CreatePlist] macOS版本：10.12.6, isOldMacOS=True
2025-08-01 09:15:39.9474 [ServiceMgr.CreatePlist] 跳过AssociatedBundleIdentifiers（旧版macOS兼容）
2025-08-01 09:15:39.9477 [ServiceMgr.CreatePlist] 强制验证：dictPlist包含AssociatedBundleIdentifiers=False
2025-08-01 09:15:39.9480 [ServiceMgr.CreatePlist] 生成的plist内容长度：1045
2025-08-01 09:15:39.9482 [ServiceMgr.CreatePlist] 确认：生成的plist不包含AssociatedBundleIdentifiers
2025-08-01 09:15:39.9487 [ServiceMgr.CreatePlist] 创建临时plist文件：/tmp/AirDroidRemoteSupport/555f3512.plist
2025-08-01 09:15:39.9489 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.service, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:15:39.9947 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:15:39.9950 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:15:39.9953 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:39.9955 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:15:39.9958 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.service, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:15:40.0423 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:15:40.0621 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:15:40.0624 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:15:40.0625 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:15:40.0631 [Program.Main] 执行AppleScript：sudo kill -9 65644
sudo launchctl unload /Library/LaunchAgents/com.sandstudio.remotesupport.proxy.plist
sudo launchctl stop com.sandstudio.remotesupport.proxy
sudo rm -f /Library/LaunchAgents/com.sandstudio.remotesupport.proxy.plist

sudo rm -f "/Library/LaunchAgents/com.sandstudio.remotesupport.proxy.plist"
sudo cp "/tmp/AirDroidRemoteSupport/ed71f92e.plist" "/Library/LaunchAgents/com.sandstudio.remotesupport.proxy.plist"
sudo chmod 755 "/Library/LaunchAgents/com.sandstudio.remotesupport.proxy.plist"

sudo launchctl unload /Library/LaunchAgents/com.sandstudio.remotesupport.video.plist
sudo launchctl stop com.sandstudio.remotesupport.video
sudo rm -f /Library/LaunchAgents/com.sandstudio.remotesupport.video.plist

sudo rm -f "/Library/LaunchAgents/com.sandstudio.remotesupport.video.plist"
sudo cp "/tmp/AirDroidRemoteSupport/eb5dc0be.plist" "/Library/LaunchAgents/com.sandstudio.remotesupport.video.plist"
sudo chmod 755 "/Library/LaunchAgents/com.sandstudio.remotesupport.video.plist"

sudo launchctl unload /Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist
sudo launchctl stop com.sandstudio.remotesupport.windows
sudo rm -f /Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist

sudo rm -f "/Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist"
sudo cp "/tmp/AirDroidRemoteSupport/d7ec0251.plist" "/Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist"
sudo chmod 755 "/Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist"

sudo launchctl unload /Library/LaunchAgents/com.sandstudio.remotesupport.safemode.plist
sudo launchctl stop com.sandstudio.remotesupport.safemode
sudo rm -f /Library/LaunchAgents/com.sandstudio.remotesupport.safemode.plist

sudo rm -f "/Library/LaunchAgents/com.sandstudio.remotesupport.safemode.plist"
sudo cp "/tmp/AirDroidRemoteSupport/5e25cec9.plist" "/Library/LaunchAgents/com.sandstudio.remotesupport.safemode.plist"
sudo chmod 755 "/Library/LaunchAgents/com.sandstudio.remotesupport.safemode.plist"

sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
sudo launchctl stop com.sandstudio.remotesupport.session
sudo rm -f /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist

sudo rm -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
sudo cp "/tmp/AirDroidRemoteSupport/075685a5.plist" "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
sudo chmod 755 "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
sudo launchctl load "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
sudo launchctl start com.sandstudio.remotesupport.session

sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist
sudo launchctl stop com.sandstudio.remotesupport.service
sudo rm -f /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist

sudo rm -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"
sudo cp "/tmp/AirDroidRemoteSupport/555f3512.plist" "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"
sudo chmod 755 "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"
sudo launchctl load "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"
sudo launchctl start com.sandstudio.remotesupport.service

touch /Library/Caches/AirDroidRemoteSupport/Service.dll

2025-08-01 09:15:41.7558 [Program.Main] 服务安装完成
2025-08-01 09:16:13.0117 Command -> t=installService	Pid=65817
CapsLock -> False
AppDomain.CurrentDomain.BaseDirectory -> /Applications/RemoteSupport.app/Contents/MonoBundle/
Application.ExecutablePath -> /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
Application.StartupPath -> /Applications/RemoteSupport.app
Environment.CurrentDirectory -> /Applications/RemoteSupport.app/Contents/Resources
Folder.AppFolder -> /Applications/RemoteSupport.app/Contents/MonoBundle/

2025-08-01 09:16:13.0233 [ServiceMgr.static] 当前用户：ceshi
2025-08-01 09:16:13.0237 [ServiceMgr.static] IsRoot=False
2025-08-01 09:16:13.0243 [ServiceMgr.static] macOS版本：10.12.6
2025-08-01 09:16:13.0246 [Program.Main] 开始检查服务状态，svrName=com.sandstudio.remotesupport.session
2025-08-01 09:16:13.0255 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.session, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:16:13.0897 [ServiceMgr.GetServiceStatus] 服务控制器状态：Stopped
2025-08-01 09:16:13.0899 [ServiceMgr.GetServiceStatus] MAC服务路径：/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport t=runService
2025-08-01 09:16:13.0901 [ServiceMgr.GetServiceStatus] 路径匹配，设置状态：Stopped
2025-08-01 09:16:13.0905 [ServiceMgr.GetServiceStatus] 最终返回状态：Stopped
2025-08-01 09:16:13.0907 [Program.Main] 服务状态检查结果：status=Stopped
2025-08-01 09:16:13.1232 [ServiceMgr.CheckCurrentProcessExist] 检查进程：cmd=t=runService, pid=0, exist=False
2025-08-01 09:16:13.1234 [Program.Main] 服务状态判断：isServiceRunning=False, isRunServiceExist=False, isInApplications=True
2025-08-01 09:16:13.1238 [Program.Main] macOS版本检查：10.12.6, isOldMacOS=True
2025-08-01 09:16:13.1239 [Program.Main] 检测到旧版macOS且服务未运行，强制启动Service模式
2025-08-01 09:16:13.1242 [Program.Main] 最终执行命令：t=runService
2025-08-01 09:16:13.1248 Command -> t=runService	Pid=65817
CapsLock -> False
AppDomain.CurrentDomain.BaseDirectory -> /Applications/RemoteSupport.app/Contents/MonoBundle/
Application.ExecutablePath -> /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
Application.StartupPath -> /Applications/RemoteSupport.app
Environment.CurrentDirectory -> /Applications/RemoteSupport.app/Contents/Resources
Folder.AppFolder -> /Applications/RemoteSupport.app/Contents/MonoBundle/

2025-08-01 09:16:13.1250 [Program.Main] 进程检查参数：cmdArgs=psn_, cmdIsEmpty=True
2025-08-01 09:16:13.1531 [Program.Main] 现有进程检查：existingPID=0
2025-08-01 09:16:13.1533 [Program.Main] 执行CommandRunService，cmdIsEmpty=True
2025-08-01 09:16:13.1535 [Program.Main] cmdIsEmpty=true，启动Session服务
2025-08-01 09:16:13.1536 [Program.Main] 检测到旧版macOS，尝试强制启动服务
2025-08-01 09:16:13.1942 [ServiceMgr.ForceStartServiceForOldMacOS] 开始强制启动服务：com.sandstudio.remotesupport.session
2025-08-01 09:16:13.1948 [ServiceMgr.ForceStartServiceForOldMacOS] plist路径：/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
2025-08-01 09:16:13.1952 [ServiceMgr.ForceStartServiceForOldMacOS] plist文件内容长度：608
2025-08-01 09:16:13.2028 [ServiceMgr.ForceStartServiceForOldMacOS] plutil验证结果：/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: OK

2025-08-01 09:16:13.2063 [ServiceMgr.ForceStartServiceForOldMacOS] stop结果：
2025-08-01 09:16:13.2100 [ServiceMgr.ForceStartServiceForOldMacOS] unload结果：/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: Invalid property list

2025-08-01 09:16:15.2192 [ServiceMgr.ForceStartServiceForOldMacOS] load结果：/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: Invalid property list

2025-08-01 09:16:17.2244 [ServiceMgr.ForceStartServiceForOldMacOS] start结果：
2025-08-01 09:16:20.2342 [ServiceMgr.ForceStartServiceForOldMacOS] 最终验证：com.sandstudio.remotesupport.session isRunning=False
2025-08-01 09:16:20.2344 [Program.Main] Session服务强制启动结果：False
2025-08-01 09:16:20.2346 [Program.Main] Session服务启动失败，尝试普通方式
2025-08-01 09:16:20.2347 [Program.Main] Service进程需要root权限，当前为普通用户，退出
2025-08-01 09:16:20.2349 [Program.Main] 请使用sudo权限运行或通过LaunchDaemon启动
