Last login: Fri Aug  1 12:07:28 on ttys000
TB-Mac-099deMac-mini10123:~ ceshi$ chmod
usage:	chmod [-fhv] [-R [-H | -L | -P]] [-a | +a | =a  [i][# [ n]]] mode|entry file ...
	chmod [-fhv] [-R [-H | -L | -P]] [-E | -C | -N | -i | -I] file ...
TB-Mac-099deMac-mini10123:~ ceshi$ chmod +x /Users/<USER>/Desktop/rs/view_existing_plist.sh 
TB-Mac-099deMac-mini10123:~ ceshi$ sudo /Users/<USER>/Desktop/rs/view_existing_plist.sh 
Password:
=== 查看现有的RemoteSupport plist文件 ===
查看时间: 2025年 8月 1日 星期五 13时13分57秒 CST

1. 列出所有RemoteSupport相关的plist文件...
LaunchDaemons目录:
-rwxr-xr-x   1 <USER>  <GROUP>  1155  8  1 12:08 com.sandstudio.remotesupport.service.plist
-rwxr-xr-x   1 <USER>  <GROUP>   720  8  1 12:08 com.sandstudio.remotesupport.session.plist

LaunchAgents目录:
-rwxr-xr-x   1 <USER>  <GROUP>   768  8  1 12:03 com.sandstudio.remotesupport.proxy.plist
-rwxr-xr-x   1 <USER>  <GROUP>   799  8  1 12:03 com.sandstudio.remotesupport.safemode.plist
-rwxr-xr-x   1 <USER>  <GROUP>   793  8  1 12:03 com.sandstudio.remotesupport.video.plist
-rwxr-xr-x   1 <USER>  <GROUP>   797  8  1 12:03 com.sandstudio.remotesupport.windows.plist

2. 查看session plist内容...
=== Session plist 存在，内容如下 ===
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<false/>
	<key>KeepAlive</key>
	<false/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.session</string>
	<key>ProgramArguments</key>
	<array>
		<string>/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport</string>
		<string>t=runService</string>
	</array>
	<key>RunAtLoad</key>
	<false/>
	<key>WorkingDirectory</key>
	<string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist>

=== Session plist 文件信息 ===
-rwxr-xr-x  1 <USER>  <GROUP>  720  8  1 12:08 /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: XML 1.0 document text, UTF-8 Unicode (with BOM) text


3. 查看service plist内容...
=== Service plist 存在，内容如下 ===
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<false/>
	<key>KeepAlive</key>
	<true/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.service</string>
	<key>ProgramArguments</key>
	<array>
		<string>/bin/sh</string>
		<string>-c</string>
		<string>sleep 3; if ([ -f "/Library/Caches/AirDroidRemoteSupport/Service.dll" ] || pgrep -f 'RemoteSupport t=runWindows' &gt; /dev/null) &amp;&amp; ! pgrep -f 'RemoteSupport t=runService' &gt; /dev/null; then launchctl unload "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"; launchctl stop com.sandstudio.remotesupport.session; launchctl load "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"; launchctl start com.sandstudio.remotesupport.session; fi</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist>

=== Service plist 文件信息 ===
-rwxr-xr-x  1 <USER>  <GROUP>  1155  8  1 12:08 /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist
/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist: XML 1.0 document text, UTF-8 Unicode (with BOM) text, with very long lines


4. 验证plist格式...
验证 /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist: OK

验证 /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: OK


5. 尝试加载session plist...
尝试加载session服务...
✅ Session plist加载成功
加载输出: /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: Invalid property list

6. 检查当前服务状态...
当前注册的remotesupport服务:

当前运行的RemoteSupport进程:

=== 查看完成 ===
TB-Mac-099deMac-mini10123:~ ceshi$ 
