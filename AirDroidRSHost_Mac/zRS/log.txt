Last login: Fri Aug  1 12:05:17 on ttys001
TB-Mac-099deMac-mini10123:~ ceshi$ chmod +x /Users/<USER>/Desktop/rs/analyze_generated_plist.TB-Mac-099deMTB-Mac-099deMac-minTB-Mac-0TB-Mac-09TB-Mac-09TB-Mac-09TB-MTB-Mac-099TTTTTTB-Mac-099deMac-mini10123:~ ceshi$ sudo /Users/<USER>/Desktop/rs/analyze_generated_plist.sh 
Password:
=== 分析代码生成的plist文件 ===
测试时间: 2025年 8月 1日 星期五 12时08分03秒 CST

1. 清理环境...
✅ 环境已清理

2. 创建成功的手动plist作为对比基准...
✅ 对比基准plist已创建

3. 运行RemoteSupport服务安装...
请注意观察安装过程中的任何错误信息...
Unknown heap type: #GUlD

Unknown heap type: #Blop

/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 12:08:04.8729 t=installService
Current User: root
2025-08-01 12:08:04.9789 [ServiceMgr] >>> com.sandstudio.remotesupport.session -> Status = None
2025-08-01 12:08:04.9825 t=installService
2025-08-01 12:08:05.1353 [ServiceMgr] >>> com.sandstudio.remotesupport.proxy -> Status = Stopped
2025-08-01 12:08:05.1870 [ServiceMgr] >>> com.sandstudio.remotesupport.video -> Status = Stopped
2025-08-01 12:08:05.2530 [ServiceMgr] >>> com.sandstudio.remotesupport.windows -> Status = Stopped
2025-08-01 12:08:05.3156 [ServiceMgr] >>> com.sandstudio.remotesupport.safemode -> Status = Stopped
2025-08-01 12:08:05.3810 [ServiceMgr] >>> com.sandstudio.remotesupport.session -> Status = None
2025-08-01 12:08:05.4571 [ServiceMgr] >>> com.sandstudio.remotesupport.session -> Status = None
2025-08-01 12:08:05.5177 [ServiceMgr] >>> com.sandstudio.remotesupport.service -> Status = None
2025-08-01 12:08:05.5761 [ServiceMgr] >>> com.sandstudio.remotesupport.service -> Status = None
sudo kill -9 78239
sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
sudo launchctl stop com.sandstudio.remotesupport.session
sudo rm -f /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist

sudo rm -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
sudo cp "/tmp/AirDroidRemoteSupport/8df8c863.plist" "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
sudo chmod 755 "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
sudo launchctl load "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
sudo launchctl start com.sandstudio.remotesupport.session

sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist
sudo launchctl stop com.sandstudio.remotesupport.service
sudo rm -f /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist

sudo rm -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"
sudo cp "/tmp/AirDroidRemoteSupport/a61268fc.plist" "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"
sudo chmod 755 "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"
sudo launchctl load "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"
sudo launchctl start com.sandstudio.remotesupport.service

touch /Library/Caches/AirDroidRemoteSupport/Service.dll

/Users/<USER>/Desktop/rs/analyze_generated_plist.sh: line 46: 78239 Killed: 9               sudo /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport

4. 查看临时生成的plist文件...
临时目录存在，查看所有plist文件：
=== /tmp/AirDroidRemoteSupport//083b440f.plist ===
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<false/>
	<key>KeepAlive</key>
	<false/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.windows</string>
	<key>LimitLoadToSessionType</key>
	<array>
		<string>Aqua</string>
	</array>
	<key>ProgramArguments</key>
	<array>
		<string>/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport</string>
		<string>t=runWindows</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist>

=== /tmp/AirDroidRemoteSupport//2c6f74a9.plist ===
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>KeepAlive</key>
	<false/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.video</string>
	<key>LimitLoadToSessionType</key>
	<array>
		<string>Aqua</string>
		<string>LoginWindow</string>
	</array>
	<key>ProgramArguments</key>
	<array>
		<string>/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport</string>
		<string>t=runVideo</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist>

=== /tmp/AirDroidRemoteSupport//6626557b.plist ===
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<false/>
	<key>KeepAlive</key>
	<false/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.safemode</string>
	<key>LimitLoadToSessionType</key>
	<array>
		<string>Aqua</string>
	</array>
	<key>ProgramArguments</key>
	<array>
		<string>/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport</string>
		<string>t=runSafeMode</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist>

=== /tmp/AirDroidRemoteSupport//8df8c863.plist ===
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<false/>
	<key>KeepAlive</key>
	<false/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.session</string>
	<key>ProgramArguments</key>
	<array>
		<string>/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport</string>
		<string>t=runService</string>
	</array>
	<key>RunAtLoad</key>
	<false/>
	<key>WorkingDirectory</key>
	<string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist>

=== /tmp/AirDroidRemoteSupport//91a63add.plist ===
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>KeepAlive</key>
	<true/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.proxy</string>
	<key>LimitLoadToSessionType</key>
	<array>
		<string>LoginWindow</string>
	</array>
	<key>ProgramArguments</key>
	<array>
		<string>/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport</string>
		<string>t=runProxy</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist>

=== /tmp/AirDroidRemoteSupport//a61268fc.plist ===
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<false/>
	<key>KeepAlive</key>
	<true/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.service</string>
	<key>ProgramArguments</key>
	<array>
		<string>/bin/sh</string>
		<string>-c</string>
		<string>sleep 3; if ([ -f "/Library/Caches/AirDroidRemoteSupport/Service.dll" ] || pgrep -f 'RemoteSupport t=runWindows' &gt; /dev/null) &amp;&amp; ! pgrep -f 'RemoteSupport t=runService' &gt; /dev/null; then launchctl unload "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"; launchctl stop com.sandstudio.remotesupport.session; launchctl load "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"; launchctl start com.sandstudio.remotesupport.session; fi</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist>


5. 查看安装到系统的plist文件...
❌ Session plist文件不存在
❌ Service plist文件不存在

6. 文件属性对比...
❌ 无法进行对比，代码生成的plist文件不存在

7. 验证plist格式...
❌ 无法验证，代码生成的plist文件不存在

8. 尝试加载代码生成的plist...
❌ 无法测试加载，代码生成的plist文件不存在

=== 分析完成 ===
测试结束时间: 2025年 8月 1日 星期五 12时08分05秒 CST
TB-Mac-099deMac-mini10123:~ ceshi$ 
