TB-Mac-099deMac-mini10123:~ ceshi$ chmod +x /Users/<USER>/Desktop/rs/fix_plist_bom_v2.sh 
TB-Mac-099deMac-mini10123:~ ceshi$ sudo /Users/<USER>/Desktop/rs/fix_plist_bom_v2.sh 
Password:
=== 修复plist文件的BOM问题 (强力版本) ===
修复时间: 2025年 8月 1日 星期五 14时05分13秒 CST

1. 检查当前文件编码...
Session plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: XML 1.0 document text, UTF-8 Unicode (with BOM) text
Service plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist: XML 1.0 document text, UTF-8 Unicode (with BOM) text, with very long lines

2. 使用hexdump查看文件开头的字节...
Session plist前16字节:
00000000  ef bb bf 3c 3f 78 6d 6c  20 76 65 72 73 69 6f 6e  |...<?xml version|
Service plist前16字节:
00000000  ef bb bf 3c 3f 78 6d 6c  20 76 65 72 73 69 6f 6e  |...<?xml version|

3. 使用多种方法移除BOM...
方法1: 使用tail跳过BOM字节...
方法2: 使用dd跳过BOM字节...
✅ BOM移除完成

4. 验证移除结果...
方法1结果:
Session plist (tail方法):
/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist.nobom: XML 1.0 document text, ASCII text
Service plist (tail方法):
/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist.nobom: XML 1.0 document text, ASCII text, with very long lines

方法2结果:
Session plist (dd方法):
/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist.nobom2: XML 1.0 document text, ASCII text
Service plist (dd方法):
/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist.nobom2: XML 1.0 document text, ASCII text, with very long lines

5. 验证plist格式...
验证tail方法的session plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist.nobom: OK
验证tail方法的service plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist.nobom: OK

验证dd方法的session plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist.nobom2: OK
验证dd方法的service plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist.nobom2: OK

6. 选择最佳方法并替换原文件...
使用tail方法的结果...
✅ 文件替换完成

7. 验证最终结果...
Session plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: XML 1.0 document text, ASCII text
Service plist:
/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist: XML 1.0 document text, ASCII text, with very long lines

8. 测试launchctl加载...
卸载现有服务...

加载session plist...
✅ Session plist加载成功

加载service plist...
✅ Service plist加载成功

9. 检查服务状态...
当前注册的remotesupport服务:
88277	0	com.sandstudio.remotesupport.service
-	0	com.sandstudio.remotesupport.session

当前运行的RemoteSupport进程:

=== 修复完成 ===
TB-Mac-099deMac-mini10123:~ ceshi$ 
