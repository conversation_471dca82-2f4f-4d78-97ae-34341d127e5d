2025-08-01 09:52:48.2561 Command -> t=installService	Pid=69256
CapsLock -> False
AppDomain.CurrentDomain.BaseDirectory -> /Applications/RemoteSupport.app/Contents/MonoBundle/
Application.ExecutablePath -> /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
Application.StartupPath -> /Applications/RemoteSupport.app
Environment.CurrentDirectory -> /Applications/RemoteSupport.app/Contents/Resources
Folder.AppFolder -> /Applications/RemoteSupport.app/Contents/MonoBundle/

2025-08-01 09:52:48.3192 [ServiceMgr.static] 当前用户：root
2025-08-01 09:52:48.3195 [ServiceMgr.static] IsRoot=True
2025-08-01 09:52:48.3201 [ServiceMgr.static] macOS版本：10.12.6
2025-08-01 09:52:48.3242 [ServiceMgr.static] 已设置日志文件夹权限
2025-08-01 09:52:48.3246 [Program.Main] 开始检查服务状态，svrName=com.sandstudio.remotesupport.session
2025-08-01 09:52:48.3263 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.session, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:52:48.4307 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:52:48.4309 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:52:48.4311 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:48.4315 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:52:48.4317 [Program.Main] 服务状态检查结果：status=None
2025-08-01 09:52:48.4319 [Program.Main] 服务状态为None，保持当前参数
2025-08-01 09:52:48.4321 [Program.Main] 最终执行命令：t=installService
2025-08-01 09:52:48.4325 Command -> t=installService	Pid=69256
CapsLock -> False
AppDomain.CurrentDomain.BaseDirectory -> /Applications/RemoteSupport.app/Contents/MonoBundle/
Application.ExecutablePath -> /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
Application.StartupPath -> /Applications/RemoteSupport.app
Environment.CurrentDirectory -> /Applications/RemoteSupport.app/Contents/Resources
Folder.AppFolder -> /Applications/RemoteSupport.app/Contents/MonoBundle/

2025-08-01 09:52:48.4327 [Program.Main] 进程检查参数：cmdArgs=psn_, cmdIsEmpty=True
2025-08-01 09:52:48.4664 [Program.Main] 现有进程检查：existingPID=0
2025-08-01 09:52:48.4666 RsAPI.Ini start
2025-08-01 09:52:48.4674 ServiceMgr.Script.Clear
2025-08-01 09:52:48.4678 [Program.Main] 开始安装服务
2025-08-01 09:52:48.4958 ScriptAppendKillPid 69255 Begin
2025-08-01 09:52:48.4960 [Program.Main] 安装RS_Proxy服务
2025-08-01 09:52:48.5158 [ServiceMgr.CreatePlist] macOS版本：10.12.6, isOldMacOS=True
2025-08-01 09:52:48.5160 [ServiceMgr.CreatePlist] 跳过AssociatedBundleIdentifiers（旧版macOS兼容）
2025-08-01 09:52:48.5162 [ServiceMgr.CreatePlist] 强制验证：dictPlist包含AssociatedBundleIdentifiers=False
2025-08-01 09:52:48.5272 [ServiceMgr.CreatePlist] 生成的plist内容长度：658
2025-08-01 09:52:48.5275 [ServiceMgr.CreatePlist] 确认：生成的plist不包含AssociatedBundleIdentifiers
2025-08-01 09:52:48.5281 [ServiceMgr.CreatePlist] 创建临时plist文件：/tmp/AirDroidRemoteSupport/590540d8.plist
2025-08-01 09:52:48.5288 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.proxy, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:52:48.5759 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:52:48.5760 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:52:48.5762 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:48.5764 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:52:48.5773 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.proxy, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:52:48.6242 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:52:48.6245 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:52:48.6248 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:48.6250 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:52:48.6252 [Program.Main] 安装RS_Video服务
2025-08-01 09:52:48.6261 [ServiceMgr.CreatePlist] macOS版本：10.12.6, isOldMacOS=True
2025-08-01 09:52:48.6263 [ServiceMgr.CreatePlist] 跳过AssociatedBundleIdentifiers（旧版macOS兼容）
2025-08-01 09:52:48.6267 [ServiceMgr.CreatePlist] 强制验证：dictPlist包含AssociatedBundleIdentifiers=False
2025-08-01 09:52:48.6271 [ServiceMgr.CreatePlist] 生成的plist内容长度：683
2025-08-01 09:52:48.6273 [ServiceMgr.CreatePlist] 确认：生成的plist不包含AssociatedBundleIdentifiers
2025-08-01 09:52:48.6276 [ServiceMgr.CreatePlist] 创建临时plist文件：/tmp/AirDroidRemoteSupport/5da4b4bf.plist
2025-08-01 09:52:48.6278 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.video, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:52:48.6756 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:52:48.6760 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:52:48.6762 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:48.6768 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:52:48.6772 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.video, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:52:48.7294 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:52:48.7296 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:52:48.7298 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:48.7300 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:52:48.7301 [Program.Main] 安装RS_Windows服务
2025-08-01 09:52:48.7313 [ServiceMgr.CreatePlist] macOS版本：10.12.6, isOldMacOS=True
2025-08-01 09:52:48.7316 [ServiceMgr.CreatePlist] 跳过AssociatedBundleIdentifiers（旧版macOS兼容）
2025-08-01 09:52:48.7320 [ServiceMgr.CreatePlist] 强制验证：dictPlist包含AssociatedBundleIdentifiers=False
2025-08-01 09:52:48.7323 [ServiceMgr.CreatePlist] 跳过Disabled键（旧版macOS兼容）
2025-08-01 09:52:48.7328 [ServiceMgr.CreatePlist] 生成的plist内容长度：656
2025-08-01 09:52:48.7330 [ServiceMgr.CreatePlist] 确认：生成的plist不包含AssociatedBundleIdentifiers
2025-08-01 09:52:48.7335 [ServiceMgr.CreatePlist] 创建临时plist文件：/tmp/AirDroidRemoteSupport/0c2d7ee8.plist
2025-08-01 09:52:48.7339 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.windows, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:52:48.7936 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:52:48.7940 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:52:48.7942 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:48.7944 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:52:48.7946 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.windows, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:52:48.8400 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:52:48.8403 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:52:48.8405 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:48.8408 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:52:48.8411 [Program.Main] 安装RS_SafeMode服务
2025-08-01 09:52:48.8421 [ServiceMgr.CreatePlist] macOS版本：10.12.6, isOldMacOS=True
2025-08-01 09:52:48.8422 [ServiceMgr.CreatePlist] 跳过AssociatedBundleIdentifiers（旧版macOS兼容）
2025-08-01 09:52:48.8424 [ServiceMgr.CreatePlist] 强制验证：dictPlist包含AssociatedBundleIdentifiers=False
2025-08-01 09:52:48.8425 [ServiceMgr.CreatePlist] 跳过Disabled键（旧版macOS兼容）
2025-08-01 09:52:48.8429 [ServiceMgr.CreatePlist] 生成的plist内容长度：658
2025-08-01 09:52:48.8430 [ServiceMgr.CreatePlist] 确认：生成的plist不包含AssociatedBundleIdentifiers
2025-08-01 09:52:48.8435 [ServiceMgr.CreatePlist] 创建临时plist文件：/tmp/AirDroidRemoteSupport/6d95d9dd.plist
2025-08-01 09:52:48.8437 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.safemode, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:52:48.8848 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:52:48.8852 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:52:48.8853 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:48.8855 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:52:48.8857 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.safemode, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:52:48.9272 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:52:48.9275 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:52:48.9277 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:48.9280 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:52:48.9281 [Program.Main] 安装RS_Session服务
2025-08-01 09:52:48.9291 [ServiceMgr.CreatePlistAsSession] 开始创建Session plist
2025-08-01 09:52:48.9292 [ServiceMgr.CreatePlistAsSession] 使用可执行文件路径：/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:48.9295 [ServiceMgr.CreatePlist] macOS版本：10.12.6, isOldMacOS=True
2025-08-01 09:52:48.9296 [ServiceMgr.CreatePlist] 跳过AssociatedBundleIdentifiers（旧版macOS兼容）
2025-08-01 09:52:48.9299 [ServiceMgr.CreatePlist] 强制验证：dictPlist包含AssociatedBundleIdentifiers=False
2025-08-01 09:52:48.9301 [ServiceMgr.CreatePlist] 跳过Disabled键（旧版macOS兼容）
2025-08-01 09:52:48.9304 [ServiceMgr.CreatePlist] 生成的plist内容长度：577
2025-08-01 09:52:48.9305 [ServiceMgr.CreatePlist] 确认：生成的plist不包含AssociatedBundleIdentifiers
2025-08-01 09:52:48.9310 [ServiceMgr.CreatePlist] 创建临时plist文件：/tmp/AirDroidRemoteSupport/52b8eadd.plist
2025-08-01 09:52:48.9311 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.session, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:52:48.9718 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:52:48.9721 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:52:48.9796 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:48.9799 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:52:48.9804 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.session, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:52:49.0209 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:52:49.0213 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:52:49.0215 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:49.0217 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:52:49.0221 [Program.Main] 安装RS服务
2025-08-01 09:52:49.0237 [ServiceMgr.CreatePlist] macOS版本：10.12.6, isOldMacOS=True
2025-08-01 09:52:49.0238 [ServiceMgr.CreatePlist] 跳过AssociatedBundleIdentifiers（旧版macOS兼容）
2025-08-01 09:52:49.0241 [ServiceMgr.CreatePlist] 强制验证：dictPlist包含AssociatedBundleIdentifiers=False
2025-08-01 09:52:49.0243 [ServiceMgr.CreatePlist] 跳过Disabled键（旧版macOS兼容）
2025-08-01 09:52:49.0246 [ServiceMgr.CreatePlist] 生成的plist内容长度：1014
2025-08-01 09:52:49.0247 [ServiceMgr.CreatePlist] 确认：生成的plist不包含AssociatedBundleIdentifiers
2025-08-01 09:52:49.0254 [ServiceMgr.CreatePlist] 创建临时plist文件：/tmp/AirDroidRemoteSupport/3efb0675.plist
2025-08-01 09:52:49.0257 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.service, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:52:49.0666 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:52:49.0669 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:52:49.0671 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:49.0674 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:52:49.0676 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.service, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:52:49.1085 [ServiceMgr.GetServiceStatus] 服务控制器状态：None
2025-08-01 09:52:49.1088 [ServiceMgr.GetServiceStatus] MAC服务路径：
2025-08-01 09:52:49.1090 [ServiceMgr.GetServiceStatus] 路径不匹配： vs /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
2025-08-01 09:52:49.1093 [ServiceMgr.GetServiceStatus] 最终返回状态：None
2025-08-01 09:52:49.1098 [Program.Main] 执行AppleScript：sudo kill -9 69255
sudo launchctl unload /Library/LaunchAgents/com.sandstudio.remotesupport.proxy.plist
sudo launchctl stop com.sandstudio.remotesupport.proxy
sudo rm -f /Library/LaunchAgents/com.sandstudio.remotesupport.proxy.plist

sudo rm -f "/Library/LaunchAgents/com.sandstudio.remotesupport.proxy.plist"
sudo cp "/tmp/AirDroidRemoteSupport/590540d8.plist" "/Library/LaunchAgents/com.sandstudio.remotesupport.proxy.plist"
sudo chmod 755 "/Library/LaunchAgents/com.sandstudio.remotesupport.proxy.plist"

sudo launchctl unload /Library/LaunchAgents/com.sandstudio.remotesupport.video.plist
sudo launchctl stop com.sandstudio.remotesupport.video
sudo rm -f /Library/LaunchAgents/com.sandstudio.remotesupport.video.plist

sudo rm -f "/Library/LaunchAgents/com.sandstudio.remotesupport.video.plist"
sudo cp "/tmp/AirDroidRemoteSupport/5da4b4bf.plist" "/Library/LaunchAgents/com.sandstudio.remotesupport.video.plist"
sudo chmod 755 "/Library/LaunchAgents/com.sandstudio.remotesupport.video.plist"

sudo launchctl unload /Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist
sudo launchctl stop com.sandstudio.remotesupport.windows
sudo rm -f /Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist

sudo rm -f "/Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist"
sudo cp "/tmp/AirDroidRemoteSupport/0c2d7ee8.plist" "/Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist"
sudo chmod 755 "/Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist"

sudo launchctl unload /Library/LaunchAgents/com.sandstudio.remotesupport.safemode.plist
sudo launchctl stop com.sandstudio.remotesupport.safemode
sudo rm -f /Library/LaunchAgents/com.sandstudio.remotesupport.safemode.plist

sudo rm -f "/Library/LaunchAgents/com.sandstudio.remotesupport.safemode.plist"
sudo cp "/tmp/AirDroidRemoteSupport/6d95d9dd.plist" "/Library/LaunchAgents/com.sandstudio.remotesupport.safemode.plist"
sudo chmod 755 "/Library/LaunchAgents/com.sandstudio.remotesupport.safemode.plist"

sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
sudo launchctl stop com.sandstudio.remotesupport.session
sudo rm -f /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist

sudo rm -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
sudo cp "/tmp/AirDroidRemoteSupport/52b8eadd.plist" "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
sudo chmod 755 "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
sudo launchctl load "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
sudo launchctl start com.sandstudio.remotesupport.session

sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist
sudo launchctl stop com.sandstudio.remotesupport.service
sudo rm -f /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist

sudo rm -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"
sudo cp "/tmp/AirDroidRemoteSupport/3efb0675.plist" "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"
sudo chmod 755 "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"
sudo launchctl load "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"
sudo launchctl start com.sandstudio.remotesupport.service

touch /Library/Caches/AirDroidRemoteSupport/Service.dll

2025-08-01 09:52:49.8649 [Program.Main] 服务安装完成
2025-08-01 09:53:03.8336 Command -> t=installService	Pid=69392
CapsLock -> False
AppDomain.CurrentDomain.BaseDirectory -> /Applications/RemoteSupport.app/Contents/MonoBundle/
Application.ExecutablePath -> /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
Application.StartupPath -> /Applications/RemoteSupport.app
Environment.CurrentDirectory -> /Applications/RemoteSupport.app/Contents/Resources
Folder.AppFolder -> /Applications/RemoteSupport.app/Contents/MonoBundle/

2025-08-01 09:53:03.8465 [ServiceMgr.static] 当前用户：ceshi
2025-08-01 09:53:03.8467 [ServiceMgr.static] IsRoot=False
2025-08-01 09:53:03.8477 [ServiceMgr.static] macOS版本：10.12.6
2025-08-01 09:53:03.8481 [Program.Main] 开始检查服务状态，svrName=com.sandstudio.remotesupport.session
2025-08-01 09:53:03.8489 [ServiceMgr.GetServiceStatus] 开始检查服务状态：serviceName=com.sandstudio.remotesupport.session, srvPath=/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport, checkServicePath=True
2025-08-01 09:53:03.9187 [ServiceMgr.GetServiceStatus] 服务控制器状态：Stopped
2025-08-01 09:53:03.9190 [ServiceMgr.GetServiceStatus] MAC服务路径：/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport t=runService
2025-08-01 09:53:03.9191 [ServiceMgr.GetServiceStatus] 路径匹配，设置状态：Stopped
2025-08-01 09:53:03.9195 [ServiceMgr.GetServiceStatus] 最终返回状态：Stopped
2025-08-01 09:53:03.9198 [Program.Main] 服务状态检查结果：status=Stopped
2025-08-01 09:53:03.9581 [ServiceMgr.CheckCurrentProcessExist] 检查进程：cmd=t=runService, pid=0, exist=False
2025-08-01 09:53:03.9583 [Program.Main] 服务状态判断：isServiceRunning=False, isRunServiceExist=False, isInApplications=True
2025-08-01 09:53:03.9588 [Program.Main] macOS版本检查：10.12.6, isOldMacOS=True
2025-08-01 09:53:03.9589 [Program.Main] 检测到旧版macOS且服务未运行，强制启动Service模式
2025-08-01 09:53:03.9592 [Program.Main] 最终执行命令：t=runService
2025-08-01 09:53:03.9597 Command -> t=runService	Pid=69392
CapsLock -> False
AppDomain.CurrentDomain.BaseDirectory -> /Applications/RemoteSupport.app/Contents/MonoBundle/
Application.ExecutablePath -> /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport
Application.StartupPath -> /Applications/RemoteSupport.app
Environment.CurrentDirectory -> /Applications/RemoteSupport.app/Contents/Resources
Folder.AppFolder -> /Applications/RemoteSupport.app/Contents/MonoBundle/

2025-08-01 09:53:03.9599 [Program.Main] 进程检查参数：cmdArgs=psn_, cmdIsEmpty=True
2025-08-01 09:53:03.9875 [Program.Main] 现有进程检查：existingPID=0
2025-08-01 09:53:03.9877 [Program.Main] 执行CommandRunService，cmdIsEmpty=True
2025-08-01 09:53:03.9879 [Program.Main] cmdIsEmpty=true，启动Session服务
2025-08-01 09:53:03.9881 [Program.Main] 检测到旧版macOS，尝试强制启动服务
2025-08-01 09:53:04.0271 [ServiceMgr.ForceStartServiceForOldMacOS] 开始强制启动服务：com.sandstudio.remotesupport.session
2025-08-01 09:53:04.0280 [ServiceMgr.ForceStartServiceForOldMacOS] plist路径：/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
2025-08-01 09:53:04.0284 [ServiceMgr.ForceStartServiceForOldMacOS] plist文件内容长度：577
2025-08-01 09:53:04.0361 [ServiceMgr.ForceStartServiceForOldMacOS] plutil验证结果：/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: OK

2025-08-01 09:53:04.0399 [ServiceMgr.ForceStartServiceForOldMacOS] stop结果：
2025-08-01 09:53:04.0436 [ServiceMgr.ForceStartServiceForOldMacOS] unload结果：/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: Invalid property list

2025-08-01 09:53:06.0518 [ServiceMgr.ForceStartServiceForOldMacOS] load结果：/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist: Invalid property list

2025-08-01 09:53:08.0605 [ServiceMgr.ForceStartServiceForOldMacOS] start结果：
2025-08-01 09:53:11.0702 [ServiceMgr.ForceStartServiceForOldMacOS] 最终验证：com.sandstudio.remotesupport.session isRunning=False
2025-08-01 09:53:11.0703 [Program.Main] Session服务强制启动结果：False
2025-08-01 09:53:11.0705 [Program.Main] Session服务启动失败，尝试普通方式
2025-08-01 09:53:11.0707 [Program.Main] Service进程需要root权限，当前为普通用户，退出
2025-08-01 09:53:11.0710 [Program.Main] 请使用sudo权限运行或通过LaunchDaemon启动
