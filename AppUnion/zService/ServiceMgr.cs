﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System.IO;
using System.ServiceProcess;

using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Android;

#if MAC
using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;
#else
using System.Drawing;
using Microsoft.Win32;
#endif

namespace iTong.Android
{
    /// <summary>
    /// 服务启动类型
    /// </summary>
    public enum StartType
    {
        /// <summary>
        /// 指定由启动加载程序加载的设备驱动程序
        /// </summary>
        boot,

        /// <summary>
        /// 指定在内核初始化期间启动的设备驱动程序
        /// </summary>
        system,

        /// <summary>
        /// 指定每次重启计算机时自动启动的服务，即使没有人登录到计算机，该服务也会运行
        /// </summary>
        auto,

        /// <summary>
        /// 指定必须手动启动的服务。 如果没有指定 start=，则此为默认值
        /// </summary>
        demand,

        /// <summary>
        /// 指定无法启动的服务。 要启动已禁用的服务，请将启动类型更改为其他某个值
        /// </summary>
        disabled,

        /// <summary>
        /// 指定在其他自动服务启动后短时间内自动启动的服务
        /// </summary>
        delayed_auto,
    }

    /// <summary>
    /// 用于管理服务
    /// </summary>
    public partial class ServiceMgr
    {
        static ServiceMgr()
        {
#if MAC
            string curUser = GetCurrentUser();

            IsRoot = curUser.TrimEnd('\n') == "root";
            if (IsRoot)
            {
                //将日志文件夹设置为
                Common.Chmod(Folder.ApplicationDataFolder, 777);
            }
#else
#endif
        }


        /// <summary>
        /// Monitor.exe路径
        /// </summary>
        public static string ServicePath
        {
            get
            {
#if MAC
                return System.Windows.Forms.Application.ExecutablePath;
#else
                string strPath = System.Windows.Forms.Application.ExecutablePath;
                if (strPath.Contains("Monitor.exe"))
                    strPath = Path.Combine(Folder.AppFolder, string.Format("{0}.exe", Folder.AppType.ToString()));

                return strPath;
#endif
            }
        }

        /// <summary>
        /// Monitor.exe路径
        /// </summary>
        public static string ServicePathForMonitor
        {
            get
            {
                if (ServicePath.Contains("Monitor.exe"))
                    return ServicePath;
                else
                {
                    string exeName = Path.GetFileNameWithoutExtension(System.Windows.Forms.Application.ExecutablePath);

                    switch (exeName)
                    {
                        case "RemoteSupport":
                            return Path.Combine(System.Windows.Forms.Application.StartupPath, "RSMonitor.exe");
                        case "BizDaemon":
                            return Path.Combine(System.Windows.Forms.Application.StartupPath, "BDMonitor.exe");
                        case "GoInsightDaemon":
                            return Path.Combine(System.Windows.Forms.Application.StartupPath, "GIMonitor.exe");
                        default:
                            break;
                    }

                    return System.Windows.Forms.Application.ExecutablePath.Replace(".exe", "Monitor.exe");
                }
            }
        }

        /// <summary>
        /// AirDroid Remote Support Host
        /// </summary>
        public static string ServiceNameForRS
        {
            get
            {
                return "AirDroid Remote Support";
            }
        }

        /// <summary>
        /// AirDroid Remote Support Monitor
        /// </summary>
        public static string ServiceNameForRSMonitor
        {
            get { return "AirDroid Remote Support Monitor"; }
        }

        /// <summary>
        /// AirDroid Biz Daemon
        /// </summary>
        public static string ServiceNameForBiz
        {
            get { return "AirDroid Biz Daemon"; }
        }

        /// <summary>
        /// AirDroid Biz Daemon Monitor
        /// </summary>
        public static string ServiceNameForBizMonitor
        {
            get { return "AirDroid Biz Daemon Monitor"; }
        }

        /// <summary>
        /// Go Insight Daemon
        /// </summary>
        public static string ServiceNameForGI
        {
            get { return "Go Insight Daemon"; }
        }

        /// <summary>
        /// Go Insight Daemon Monitor
        /// </summary>
        public static string ServiceNameForGIMonitor
        {
            get { return "Go Insight Daemon Monitor"; }
        }

        public static void Log(string msg, string className = "ServiceMgr")
        {
            MyLog.Log(msg, className, true, Color.Black, "Session");
        }

        /// <summary>
        /// 获取服务安装路径
        /// </summary>
        public static string GetServiceInstallPath(string srvName = "", string keyName = "ImagePath")
        {
            string keyPath = string.Format(@"SYSTEM\CurrentControlSet\Services\{0}", srvName);
            string strValue = MyRegistry.GetRegisterData<string>(keyName, keyPath, Registry.LocalMachine);

            return strValue;
        }

        /// <summary>
        /// 判断服务是否存在
        /// </summary>
        public static bool IsServiceExisted(string serviceName, string srvPath, bool checkServiceRunning = false)
        {
            bool blnResult = false;


            ServiceController[] services = null;
            try
            {
                services = ServiceController.GetServices();
                foreach (ServiceController sc in services)
                {
                    if (string.Compare(sc.ServiceName, serviceName, true) == 0)
                    {
#if MAC
                        string pathService = sc.Command;
#else
                        string pathService = GetServiceInstallPath(serviceName);
#endif
                        if (pathService.ToLower().Contains(srvPath.ToLower()))
                        {
                            if (checkServiceRunning)
                                blnResult = sc.Status == ServiceControllerStatus.Running;
                            else
                                blnResult = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "IsServiceExisted");

                if (services != null)
                {
                    foreach (ServiceController sc in services)
                        sc.Dispose();

                    services = null;
                }
            }


            return blnResult;
        }

        /// <summary>
        /// 判断服务是否存在
        /// </summary>
        public static ServiceControllerStatus GetServiceStatus(string serviceName, string srvPath = "", bool checkServicePath = true)
        {
            ServiceControllerStatus status = (ServiceControllerStatus)0;

            using (ServiceController sc = new ServiceController(serviceName))
            {
                try
                {
                    Log(string.Format("{0} -> Status = {1}", sc.ServiceName, sc.Status.ToString()));

                    if (checkServicePath)
                    {
#if MAC
                        string pathService = sc.Command;
#else
                        string pathService = GetServiceInstallPath(serviceName);
#endif
                        if (pathService.ToLower().Contains(srvPath.ToLower()))
                        {
                            status = sc.Status;
                        }
                    }
                    else
                    {
                        status = sc.Status;
                    }

                }
                catch (Exception ex)
                {
                    //服务不存在，尝试创建服务
                    Log("GetServiceStatus\r\n" + ex.ToString());
                }
            }

            //ServiceMgr.WriteLine(string.Format("svrName = {0}, Path = {1}, status = {2}", serviceName, srvPath, status.ToString()));

            return status;
        }

        /// <summary>
        /// 判断服务是否存在
        /// </summary>
        public static bool IsServiceExistedNew(string serviceName, string srvPath = "", bool checkServicePath = true)
        {
            ServiceControllerStatus status = GetServiceStatus(serviceName, srvPath, checkServicePath);

            bool blnResult = ((int)status != 0);
            //            bool blnResult = false;

            //            using (ServiceController sc = new ServiceController(serviceName))
            //            {
            //                try
            //                {
            //                    Log(string.Format("{0} -> Status = {1}", sc.ServiceName, sc.Status.ToString()));

            //                    if (checkServicePath)
            //                    {
            //#if MAC
            //                        string pathService = sc.Command;
            //#else
            //                        string pathService = GetServiceInstallPath(serviceName);
            //#endif
            //                        if (pathService.ToLower().Contains(srvPath.ToLower()))
            //                        {
            //                            if (checkServiceRunning)
            //                                blnResult = sc.Status == ServiceControllerStatus.Running;
            //                            else
            //                                blnResult = true;
            //                        }
            //                    }
            //                    else
            //                    {
            //                        blnResult = true;
            //                    }
            //                }
            //                catch (Exception ex)
            //                {
            //                    //服务不存在，尝试创建服务
            //                    Log("IsServiceExistedNew\r\n" + ex.ToString());
            //                }
            //            }

            return blnResult;
        }


        /// <summary>
        /// 设置服务启动类型
        /// </summary>
        public static bool ServiceStartType(string srvName, string srvPath = "", StartType startType = StartType.auto)
        {
            bool blnResult = false;            

#if MAC


#else
            if (string.IsNullOrEmpty(srvPath))
                srvPath = System.Windows.Forms.Application.ExecutablePath;

            if (string.IsNullOrEmpty(srvName))
                srvName = Path.GetFileNameWithoutExtension(srvPath);

            using (ServiceController sc = new ServiceController(srvName))
            {
                try
                {
                    Log(string.Format("{0} ->Current Status = {1}", sc.ServiceName, sc.Status.ToString()));

                    string pathService = GetServiceInstallPath(srvName);
                    if (pathService.ToLower().Contains(srvPath.ToLower()))
                    {

                        Log(string.Format("{0} ->Set Status = {1}", sc.ServiceName, startType.ToString()));

                        string cmd = "sc config \"{0}\" binpath= \"{1} {2}\"  type= own type= interact start= {4} displayname= \"{3}\"";
                        string cmdFormat = string.Format(cmd, srvName, srvPath, ParaMgr.CommandRunService, srvName, startType.ToString().Replace("_", "-"));

                        string strLog = Common.ProcessStartUntilFinish(cmdFormat, false, System.Text.Encoding.Default);

                        blnResult = true;
                    }
                }
                catch (Exception ex)
                {
                    //修改服务启动类型失败
                    Log("ServiceStartType\r\n" + ex.ToString());
                }
            }
#endif

            return blnResult;
        }

        /// <summary>
        /// 安装服务
        /// </summary>
        public static bool ServiceInstall(string srvName, string srvPath = "", StartType startType = StartType.auto, string plistPathSource = "", bool isLaunchDaemon = false)
        {
            bool blnResult = false;

            try
            {
#if MAC
                if (string.IsNullOrEmpty(srvPath))
                    srvPath = System.Windows.Forms.Application.ExecutablePath;

                if (string.IsNullOrEmpty(srvName))
                {
                    Log("ServiceInstall srvName IsNullOrEmpty.");
                    goto DoExit;
                }

                if (string.IsNullOrEmpty(plistPathSource))
                {
                    Log("ServiceInstall plistPathSource IsNullOrEmpty.");
                    goto DoExit;
                }

                if (!File.Exists(plistPathSource))
                {
                    Log("ServiceInstall plistPathSource is not exist.\t" + plistPathSource);
                    goto DoExit;
                }

                if (IsServiceExistedNew(srvName, srvPath))
                {
                    blnResult = true;
                    goto DoExit;
                }
                else
                {
                    ServiceUninstall(srvName, isLaunchDaemon);
                }

                string plistPathSourceFormat = plistPathSource.ToArgPath();
                string plistPathFormat = ServiceMgr.GetServicePathByName(srvName, isLaunchDaemon);

                StringBuilder sb = new StringBuilder();
                sb.AppendLine(string.Format("sudo rm -f {0}", plistPathFormat));
                sb.AppendLine(string.Format("sudo cp {0} {1}", plistPathSourceFormat, plistPathFormat));
                sb.AppendLine(string.Format("sudo chmod 755 {0}", plistPathFormat));

                if (startType == StartType.auto)
                {
                    sb.AppendLine(string.Format("sudo launchctl load {0}", plistPathFormat));

                    ////若plist文件中RunAtLoad=false，则再运行start命令
                    //bool RunAtLoad = false;
                    //Dictionary<object, object> dictPlist = iTong.Device.CoreFoundation.ReadPlist_managed(plistPath) as Dictionary<object, object>;
                    //if (dictPlist != null && dictPlist.ContainsKey("RunAtLoad"))
                    //    RunAtLoad = (bool)dictPlist["RunAtLoad"];

                    //if (!RunAtLoad)
                    {
                        //即使plist文件中RunAtLoad=true，也要重新调用start，避免有时候服务已经加载，但是服务没有被正常唤起执行
                        sb.AppendLine(string.Format("sudo launchctl start {0}", srvName));
                    }
                }


                //string strLog = Utils.RunScript(sb.ToString());
                mScriptBuilder.AppendLine(sb.ToString());

#else
                if (string.IsNullOrEmpty(srvPath))
                    srvPath = System.Windows.Forms.Application.ExecutablePath;

                if (string.IsNullOrEmpty(srvName))
                    srvName = Path.GetFileNameWithoutExtension(srvPath);

                if (IsServiceExistedNew(srvName, srvPath))
                {
                    blnResult = true;
                    goto DoExit;
                }
                else
                {
                    ServiceUninstall(srvName);
                }

                //sc.exe [<servername>] create [<servicename>] [type= {own | share | kernel | filesys | rec | interact type= {own | share}}] [start= {boot | system | auto | demand | disabled | delayed-auto}] [error= {normal | severe | critical | ignore}] [binpath= <binarypathname>] [group= <loadordergroup>] [tag= {yes | no}] [depend= <dependencies>] [obj= {<accountname> | <objectname>}] [displayname= <displayname>] [password= <password>]
                //sc delete "AirDroid Remote Support Host"
                //sc Create "AirDroid Remote Support Host" binpath= "E:\GitCode\Tongbu_Assistant_Source\bin\x86\Debug\AirDroidRsHost.exe t=runService" type= own type= interact start= auto displayname= "AirDroid Remote Support Host"
                string cmd = "sc create \"{0}\" binpath= \"{1} {2}\"  type= own start= {4} displayname= \"{3}\"";
                string cmdFormat = string.Format(cmd, srvName, srvPath, ParaMgr.CommandRunService, srvName, startType.ToString().Replace("_", "-"));

                string strLog = Common.ProcessStartUntilFinish(cmdFormat, false, System.Text.Encoding.Default);
                MyLog.WriteLine(cmdFormat + "\t" + strLog);

                ////设置服务自启动失败时候做的依赖操作
                //cmd = "sc failure \"{0}\" reset=0 actions=run/5000// command= \"\\\"{1}\"\\\"";
                //cmdFormat = string.Format(cmd, srvName, srvPath);

                //strLog = Common.ProcessStartUntilFinish(cmdFormat, false, System.Text.Encoding.Default);

                //MyLog.WriteLine(cmdFormat + "\t" + strLog);
                //using (TransactedInstaller transactedInstaller = new TransactedInstaller())
                //{
                //    using (AssemblyInstaller installer = new AssemblyInstaller())
                //    {
                //        installer.Path = srvPath;
                //        installer.CommandLine = new string[] { ParaMgr.CommandRunService };
                //        installer.UseNewContext = true;

                //        transactedInstaller.Installers.Add(installer);
                //        transactedInstaller.Install(new Hashtable());
                //    }
                //}
#endif

                if (IsServiceExistedNew(srvName, srvPath))
                {
                    blnResult = true;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ServiceInstall");
            }

        DoExit:
            //自动开启服务
            if (blnResult && startType == StartType.auto)
                blnResult = ServiceStart(srvName);

            return blnResult;
        }

        /// <summary>
        /// 卸载服务
        /// </summary>
        public static bool ServiceUninstall(string srvName, bool isLaunchDaemon = false)
        {
            bool blnResult = false;

            try
            {
#if MAC
                string plistPath = Path.Combine(isLaunchDaemon ? PathLaunchDaemons : PathLaunchAgents, string.Format("{0}.plist", srvName));
                //if (!File.Exists(plistPath))
                //{
                //    blnResult = true;
                //    goto DoExit;
                //}

                string plistPathFormat = plistPath.Replace(" ", "\\ ");

                StringBuilder sb = new StringBuilder();
                sb.AppendLine(string.Format("sudo launchctl unload {0}", plistPathFormat));
                sb.AppendLine(string.Format("sudo launchctl stop {0}", srvName));                
                sb.AppendLine(string.Format("sudo rm -f {0}", plistPathFormat));

                //string strLog = Utils.RunScript(sb.ToString());
                mScriptBuilder.AppendLine(sb.ToString());              

#else
                ServiceStop(srvName);

                string cmd = "sc delete \"{0}\"";
                string cmdFormat = string.Format(cmd, srvName);

                string strLog = Common.ProcessStartUntilFinish(cmdFormat, false, System.Text.Encoding.Default);
                MyLog.WriteLine(cmdFormat + "\t" + strLog);
                //using (AssemblyInstaller installer = new AssemblyInstaller())
                //{
                //    installer.UseNewContext = true;
                //    installer.Path = serviceFilePath;
                //    installer.Uninstall(null);
                //}               
#endif
                blnResult = true;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ServiceUninstall");
            }

        DoExit:
            return blnResult;
        }


        /// <summary>
        /// 判断服务是否已经在运行
        /// </summary>
        public static bool IsServiceRunning(string serviceName)
        {
            bool blnResult = false;

            try
            {
                using (ServiceController control = new ServiceController(serviceName))
                {
                    if (control.Status == ServiceControllerStatus.Running)
                    {
                        blnResult = true;
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "IsServiceRunning");
            }

            return blnResult;
        }

        /// <summary>
        /// 启动服务
        /// </summary>
        public static bool ServiceStart(string serviceName)
        {
            bool blnResult = false;

            try
            {
                using (ServiceController control = new ServiceController(serviceName))
                {
                    try
                    {
                        if (control.Status == ServiceControllerStatus.Running)
                        {
                            blnResult = true;
                        }
                        else
                        {
                            control.Start();
                            blnResult = true;
                        }

                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex, "ServiceStart.1");
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ServiceStart");
            }

            return blnResult;
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        public static bool ServiceStop(string serviceName)
        {
            bool blnResult = false;

            try
            {
                using (ServiceController control = new ServiceController(serviceName))
                {
                    try
                    {
                        if (control.Status == ServiceControllerStatus.Running)
                        {
                            control.Stop();
                            blnResult = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex, "ServiceStop.1");
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ServiceStop");
            }

            return blnResult;
        }

        /// <summary>
        /// 退出当前程序，通过Monitor服务来重启程序
        /// </summary>
        public static void RestartAppByError(string msg)
        {
            string srvName = string.Empty;
            string srvPath = string.Empty;

            switch (Folder.AppType)
            {
                case RunType.BizDaemon:
                    srvName = ServiceMgr.ServiceNameForBizMonitor;
                    srvPath = ServiceMgr.ServicePathForMonitor;
                    break;

                case RunType.RemoteSupport:
                    srvName = ServiceMgr.ServiceNameForRSMonitor;
                    srvPath = ServiceMgr.ServicePathForMonitor;
                    break;

                case RunType.GoInsightDaemon:
                    srvName = ServiceMgr.ServiceNameForGIMonitor;
                    srvPath = ServiceMgr.ServicePathForMonitor;
                    break;
            }

            if (!string.IsNullOrEmpty(srvName) && ServiceMgr.IsServiceExisted(srvName, srvPath))
            {
                MyLog.WriteLine("RestartAppByError -> " + msg, "Dll", true);

                //退出程序，释放已有连接
                SocketMgr.KillCurrentApp();
            }
        }


        /// <summary>
        /// 获取服务的运行类型
        /// </summary>
        public static StartType GetServiceStart(string serviceName)
        {
            StartType blnResult = StartType.demand;

#if !MAC
            try
            {
                using (ServiceController control = new ServiceController(serviceName))
                {
                    int val = MyRegistry.GetRegisterData<int>("Start", $@"SYSTEM\CurrentControlSet\Services\{control.ServiceName}", Registry.LocalMachine);
                    switch (val)
                    {
                        case 2:
                            blnResult = StartType.auto;
                            break;
                        case 3:
                            blnResult = StartType.demand;
                            break;
                        case 4:
                            blnResult = StartType.disabled;
                            break;
                        default:
                            blnResult = StartType.demand;
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "IsServiceRunning");
            }
#endif

            return blnResult;
        }
    }
}
