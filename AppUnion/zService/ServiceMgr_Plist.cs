﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System.IO;
using System.ServiceProcess;
using System.Runtime.InteropServices;
using System.Windows.Forms;

using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Android;
using iTong.Device;

#if MAC
using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;
#else
using System.Drawing;
using Microsoft.Win32;
#endif

namespace iTong.Android
{
    public partial class ServiceMgr
    {
        protected static StringBuilder mScriptBuilder = new StringBuilder();

        public static StringBuilder Script
        {
            get
            {
                return mScriptBuilder;
            }
        }

        public static void ScriptClear()
        {
            mScriptBuilder.Clear();
        }

        public static void ScriptAppendLaunchctlLoad(string serviceName)
        {
            ScriptAppend(string.Format("launchctl load {0}", GetServicePlistPath(serviceName)));
            ScriptAppend(string.Format("launchctl start {0}", serviceName));
        }

        public static void ScriptAppendKillSelf()
        {
            MyLog.WriteLine("ScriptAppendKillSelf Begin");
            Process p = Process.GetCurrentProcess();
            ScriptAppendKillPid(p.Id);
        }

        public static void ScriptAppendKillPid(int pid)
        {
            MyLog.WriteLine(string.Format("ScriptAppendKillPid {0} Begin",pid));
            ScriptAppend(string.Format("sudo kill -9 {0}", pid));
        }

        public static void ScriptAppendCreateServiceDll()
        {
            ScriptAppend("touch /Library/Caches/AirDroidRemoteSupport/Service.dll");
        }

        public static void ScriptAppend(string cmd)
        {
            mScriptBuilder.AppendLine(cmd);
        }

        public static void ScriptRun()
        {
            try
            {
                string strScript = mScriptBuilder.ToString();
                if (string.IsNullOrEmpty(strScript))
                    return;


            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ScriptRun");
            }
        }

        /// <summary></summary>
        /// <param name="isRetainedData">是否保留个人数据</param>
        /// <returns></returns>
        public static void UninstallAppScript(bool isRetainedData = true)
        {
            ServiceMgr.ScriptClear();

            ServiceMgr.ServiceUninstall(ServiceMgr.ServiceNameForRS_Service, true);     //卸载RS服务
            ServiceMgr.ServiceUninstall(ServiceMgr.ServiceNameForRS_Session, true);     //卸载RS_Session服务
            ServiceMgr.ServiceUninstall(ServiceMgr.ServiceNameForRS_Windows);           //卸载RS_Windows服务
            ServiceMgr.ServiceUninstall(ServiceMgr.ServiceNameForRS_SafeMode);          //卸载RS_SafeMode服务
            ServiceMgr.ServiceUninstall(ServiceMgr.ServiceNameForRS_Video);             //卸载RS_Video服务
            ServiceMgr.ServiceUninstall(ServiceMgr.ServiceNameForRS_Proxy);             //卸载RS_Proxy服务

            ServiceMgr.ScriptAppend("sudo rm -rf '/Applications/Remote Support.app'");
            ServiceMgr.ScriptAppend("sudo rm -rf '/Applications/RemoteSupport.app'");

            if (!isRetainedData)
            {
                ServiceMgr.ScriptAppend("sudo rm -rf /Library/Caches/AirDroidRemoteSupport");
            }

            ServiceMgr.ScriptAppend("sudo pkill -f \"Remote Support\"");
            ServiceMgr.ScriptAppend("sudo pkill -f \"RemoteSupport\"");

            ServiceMgr.RunAppleScript(ServiceMgr.Script.ToString());
        }

        /// <summary>
        /// /Library/LaunchDaemons
        /// </summary>
        public static string PathLaunchDaemons
        {
            get
            {
                return "/Library/LaunchDaemons";
            }
        }

        /// <summary>
        /// /Library/LaunchAgents
        /// </summary>
        public static string PathLaunchAgents
        {
            get
            {
                return "/Library/LaunchAgents";
            }
        }

        /// <summary>
        /// /bin/launchctl
        /// </summary>
        public static string PathLaunchctl
        {
            get
            {
                return "/bin/launchctl";
            }
        }

        /// <summary>
        /// com.sandstudio.remotesupport
        /// </summary>
        public static string ServiceNameForRS_Host { get; } = "com.sandstudio.remotesupport";

        /// <summary>
        /// com.sandstudio.remotesupport.service
        /// </summary>
        public static string ServiceNameForRS_Service
        {
            get
            {
                return ServiceNameForRS_Host + ".service";
            }
        }

        /// <summary>
        /// com.sandstudio.remotesupport.proxy
        /// </summary>
        public static string ServiceNameForRS_Proxy
        {
            get
            {
                return ServiceNameForRS_Host + ".proxy";
            }
        }

        /// <summary>
        /// com.sandstudio.remotesupport.video
        /// </summary>
        public static string ServiceNameForRS_Video
        {
            get
            {
                return ServiceNameForRS_Host + ".video";
            }
        }

        /// <summary>
        /// com.sandstudio.remotesupport.windows
        /// </summary>
        public static string ServiceNameForRS_Windows
        {
            get
            {
                return ServiceNameForRS_Host + ".windows";
            }
        }

        /// <summary>
        /// com.sandstudio.remotesupport.screen
        /// </summary>
        public static string ServiceNameForRS_Screen
        {
            get
            {
                return ServiceNameForRS_Host + ".screen";
            }
        }

        /// <summary>
        /// com.sandstudio.remotesupport.safemode
        /// </summary>
        public static string ServiceNameForRS_SafeMode
        {
            get
            {
                return ServiceNameForRS_Host + ".safemode";
            }
        }

        public static string ServiceNameForRS_Session
        {
            get
            {
                return ServiceNameForRS_Host + ".session";
            }
        }

        public static string ServicePathForRS
        {
            get
            {
                return Application.ExecutablePath;
            }
        }

        public static bool IsRoot { get; private set; } = false;

        // Importing necessary SystemConfiguration functions
        [DllImport("/System/Library/Frameworks/SystemConfiguration.framework/SystemConfiguration")]
        private static extern IntPtr SCDynamicStoreCreate(IntPtr allocator, IntPtr name, IntPtr callout, IntPtr context);

        [DllImport("/System/Library/Frameworks/SystemConfiguration.framework/SystemConfiguration")]
        private static extern IntPtr SCDynamicStoreCopyConsoleUser(IntPtr store, out uid_t uid, IntPtr gid);

        // Define uid_t to match the native type
        private struct uid_t
        {
            public uint Value;
        }

        public static string GetConsoleUserName(out uint userID)
        {
            string userName = string.Empty;

            uid_t uid;
            IntPtr hStore = IntPtr.Zero;
            IntPtr hName = IntPtr.Zero;

            userID = 0;
            MyLog.WriteLine("[ServiceMgr.GetConsoleUserName] 开始获取控制台用户名");

            try
            {
                // Create a dynamic store reference
                CFString cfName = new CFString("GetConsoleUser");
                hStore = SCDynamicStoreCreate(IntPtr.Zero, cfName.Handle, IntPtr.Zero, IntPtr.Zero);
                cfName.Dispose();

                if (hStore == IntPtr.Zero)
                {
                    Log("SCDynamicStoreCreate GetConsoleUser failed.");
                    MyLog.WriteLine("[ServiceMgr.GetConsoleUserName] SCDynamicStoreCreate失败");
                    goto DoExit;
                }

                // Copy the console user
                hName = SCDynamicStoreCopyConsoleUser(hStore, out uid, IntPtr.Zero);

                if (hName == IntPtr.Zero)
                {
                    // If there's no console user, assume pre-login
                    MyLog.WriteLine("[ServiceMgr.GetConsoleUserName] SCDynamicStoreCopyConsoleUser返回空，可能是登录前状态");
                    goto DoExit;
                }

                userName = iTong.Device.CoreFoundation.ReadCFStringFromIntPtr(hName);
                userID = uid.Value;
                MyLog.WriteLine($"[ServiceMgr.GetConsoleUserName] 成功获取用户信息：userName={userName}, userID={userID}");
            }
            catch (Exception ex)
            {
                MyLog.WriteLine($"[ServiceMgr.GetConsoleUserName] 获取控制台用户名异常：{ex.Message}");
            }
            finally
            {
                if (hStore != IntPtr.Zero)
                    iTong.Device.CoreFoundation.CFRelease(hStore);

                if (hName != IntPtr.Zero)
                    iTong.Device.CoreFoundation.CFRelease(hName);
            }

        DoExit:
            WriteLine(string.Format("GetConsoleUserName -> userName = {0}, userID = {1}", userName, userID));
            MyLog.WriteLine($"[ServiceMgr.GetConsoleUserName] 最终结果：userName={userName}, userID={userID}");
            return userName;
        }

        public static string GetConsoleUserName()
        {
            uint userID;

            return GetConsoleUserName(out userID);
        }

        public static uint GetConsoleUserID()
        {
            uint userID;

            GetConsoleUserName(out userID);

            return userID;
        }

        public static bool IsLoginWindowUser()
        {
            string userName = GetConsoleUserName();
            bool result = string.IsNullOrEmpty(userName) || userName.Contains("loginwindow");
            MyLog.WriteLine($"[ServiceMgr.IsLoginWindowUser] userName={userName}, result={result}");

            return result;
        }

        public static bool IsUserLogin()
        {
            string userName = GetConsoleUserName();
            bool result = !string.IsNullOrEmpty(userName) && !userName.Contains("loginwindow");
            MyLog.WriteLine($"[ServiceMgr.IsUserLogin] userName={userName}, result={result}");

            return result;
        }

        public static string GetCurrentUser()
        {
            string userName = string.Empty;

            try
            {
                userName = MyAPI.Username;

                if (string.IsNullOrEmpty(userName))
                    userName = NSProcessInfo.ProcessInfo.GetUserName();
            }
            catch (Exception ex)
            {
                userName = Common.RunShell("/usr/bin/whoami");
            }

            WriteLine(string.Format("GetCurrentUser -> {0}", userName));

            return userName;
        }

        public static bool CheckCurrentUserIsAdmin(string userName = "")
        {
            bool blnResult = false;

            try
            {
                if (string.IsNullOrEmpty(userName))
                    userName = GetConsoleUserName();

                if (string.IsNullOrEmpty(userName) || userName == "loginwindow")
                    goto DoExit;

                string strLog = Common.RunShell("/usr/bin/id", "-Gn", userName);
                if (strLog.Contains("admin"))
                    blnResult = true;
            }
            catch (Exception ex)
            {

            }

        DoExit:
            return blnResult;
        }

        public static void Chown(string strDir = "")
        {
            if (!IsRoot)
                return;

            if (string.IsNullOrEmpty(strDir))
                strDir = Folder.ApplicationDataFolder;

            string userName = GetConsoleUserName();
            if (string.IsNullOrEmpty(userName) || userName == "loginwindow")
                return;

            if (!CheckCurrentUserIsAdmin())
                return;

            Common.Chown(strDir, userName);
        }

        public static bool LaunchApp(string serviceName, string exePath, params string[] exeArgs)
        {
            bool blnResult = true;
            MyLog.WriteLine($"[ServiceMgr.LaunchApp] 开始启动应用：serviceName={serviceName}, exePath={exePath}, exeArgs={exeArgs?.Join()}");

            bool isRoot = IsRoot;
            bool isLoginWindow = IsLoginWindowUser();
            MyLog.WriteLine($"[ServiceMgr.LaunchApp] 环境检查：IsRoot={isRoot}, IsLoginWindowUser={isLoginWindow}");

            if (isRoot && !isLoginWindow)
            {
                SocketMgr.LogApp(string.Format("LaunchApp -> serviceName={0}", serviceName));
                MyLog.WriteLine($"[ServiceMgr.LaunchApp] 以Root权限启动服务：{serviceName}");

                //用户已经登录，非loginWindow用户
                LaunchctlLoad(serviceName);
            }
            else
            {
                SocketMgr.LogApp(string.Format("RunApp -> exePath={0}, exeArgs={1}", exePath, exeArgs.Join()));
                MyLog.WriteLine($"[ServiceMgr.LaunchApp] 以普通用户权限启动应用：{exePath}");

                Common.RunApp(exePath, exeArgs);
            }

            MyLog.WriteLine($"[ServiceMgr.LaunchApp] 启动完成，结果：{blnResult}");
            return blnResult;
        }

        public static string GetServicePlistPath(string serviceName)
        {
            string plistName = string.Format("{0}.plist", serviceName);
            string plistPath = Path.Combine(PathLaunchAgents, plistName);

            if (!File.Exists(plistPath))
            {
                plistPath = Path.Combine(PathLaunchDaemons, plistName);
                if (!File.Exists(plistPath))
                    return string.Empty;
            }

            return plistPath;
        }

        public static void LaunchctlLoad(string serviceName, bool autoStart = true)
        {
            MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] 开始加载服务：serviceName={serviceName}, autoStart={autoStart}");

            string plistPath = GetServicePlistPath(serviceName);
            MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] plist路径：{plistPath}");

            if (string.IsNullOrEmpty(plistPath))
            {
                WriteLine(string.Format("LaunchctlLoad failed, Service is not exist. -> {0}", serviceName));
                MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] 服务不存在，退出：{serviceName}");
                return;
            }

            //先加载服务
            string strLog = string.Empty;
            bool isRoot = IsRoot;
            bool isLaunchAgents = plistPath.StartsWith(PathLaunchAgents, StringComparison.OrdinalIgnoreCase);
            MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] 环境检查：IsRoot={isRoot}, isLaunchAgents={isLaunchAgents}");

            if (isRoot && isLaunchAgents)// && !IsLoginWindowUser()
            {
                //LaunchctlStop(serviceName);

                //用户已经登录，非loginWindow用户
                uint consoleUserID = GetConsoleUserID();
                string guiUserID = string.Format("gui/{0}", consoleUserID);
                MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] 使用bootstrap方式加载，consoleUserID={consoleUserID}, guiUserID={guiUserID}");

                // 检查macOS版本，决定使用哪种命令
                try
                {
                    var osVersion = Common.OSVersion;
                    MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] macOS版本：{osVersion}");

                    if (osVersion < new Version("10.13"))
                    {
                        MyLog.WriteLine("[ServiceMgr.LaunchctlLoad] macOS < 10.13，使用传统load命令");

                        // 先尝试unload，避免重复加载错误
                        strLog = Common.RunShell(PathLaunchctl, "unload", plistPath);
                        MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] unload命令结果：{strLog}");

                        strLog = Common.RunShell(PathLaunchctl, "load", plistPath);
                        MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] load命令结果：{strLog}");

                        if (autoStart)
                        {
                            // 等待一下让服务加载完成
                            System.Threading.Thread.Sleep(1000);

                            strLog = Common.RunShell(PathLaunchctl, "start", serviceName);
                            MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] start命令结果：{strLog}");

                            // 验证服务是否真正启动
                            System.Threading.Thread.Sleep(2000);
                            var runningServices = GetRunningServices();
                            bool isRunning = runningServices.Values.Contains(serviceName);
                            MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] 服务启动验证：{serviceName} isRunning={isRunning}");

                            if (!isRunning)
                            {
                                MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] 服务未成功启动，尝试重新启动：{serviceName}");
                                // 再次尝试启动
                                strLog = Common.RunShell(PathLaunchctl, "kickstart", "-k", serviceName);
                                MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] kickstart命令结果：{strLog}");
                            }
                        }
                    }
                    else
                    {
                        MyLog.WriteLine("[ServiceMgr.LaunchctlLoad] macOS >= 10.13，使用bootstrap命令");
                        strLog = Common.RunShell(PathLaunchctl, "bootout", guiUserID, plistPath);
                        MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] bootout命令结果：{strLog}");

                        strLog = Common.RunShell(PathLaunchctl, "bootstrap", guiUserID, plistPath);
                        MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] bootstrap命令结果：{strLog}");
                    }
                }
                catch (Exception ex)
                {
                    MyLog.WriteLine($"[ServiceMgr.LaunchctlLoad] 获取macOS版本失败，使用默认方式：{ex.Message}");
                    strLog = Common.RunShell(PathLaunchctl, "bootout", guiUserID, plistPath);
                    strLog = Common.RunShell(PathLaunchctl, "bootstrap", guiUserID, plistPath);
                }

                //LaunchctlStart(serviceName);
            }
            else
            {
                strLog = Common.RunShell(PathLaunchctl, "load", plistPath);

                if (!autoStart)
                    return;

                ////若plist文件中RunAtLoad=false，则再运行start命令
                //bool RunAtLoad = false;
                //Dictionary<object, object> dictPlist = iTong.Device.CoreFoundation.ReadPlist_managed(plistPath) as Dictionary<object, object>;
                //if (dictPlist != null && dictPlist.ContainsKey("RunAtLoad"))
                //    RunAtLoad = (bool)dictPlist["RunAtLoad"];

                //if (!RunAtLoad)
                {
                    //即使plist文件中RunAtLoad=true，也要重新调用start，避免有时候服务已经加载，但是服务没有被正常唤起执行
                    LaunchctlStart(serviceName);
                }
            }
        }

        public static void LaunchctlUnload(string serviceName)
        {
            string plistPath = GetServicePlistPath(serviceName);
            string strLog = string.Empty;

            if (string.IsNullOrEmpty(plistPath))
            {
                WriteLine(string.Format("LaunchctlUnload failed, Service is not exist. -> {0}", serviceName));
                strLog = Common.RunShell(PathLaunchctl, "stop", serviceName);
                return;
            }

            if (IsRoot && plistPath.StartsWith(PathLaunchAgents, StringComparison.OrdinalIgnoreCase))// && !IsLoginWindowUser()
            {
                string guiUserID = string.Format("gui/{0}", GetConsoleUserID());
                //用户已经登录，非loginWindow用户
                strLog = Common.RunShell(PathLaunchctl, "bootout", guiUserID, plistPath);
            }
            else
            {
                //先加载服务
                strLog = Common.RunShell(PathLaunchctl, "unload", plistPath);
            }
        }

        public static void LaunchctlStart(string serviceName)
        {
            MyLog.WriteLine($"[ServiceMgr.LaunchctlStart] 启动服务：{serviceName}");
            string result = Common.RunShell(PathLaunchctl, "start", serviceName);
            MyLog.WriteLine($"[ServiceMgr.LaunchctlStart] 启动结果：{result}");
        }

        public static void LaunchctlStop(string serviceName)
        {
            MyLog.WriteLine($"[ServiceMgr.LaunchctlStop] 停止服务：{serviceName}");
            string result = Common.RunShell(PathLaunchctl, "stop", serviceName);
            MyLog.WriteLine($"[ServiceMgr.LaunchctlStop] 停止结果：{result}");
        }

        public static List<string> LaunchctlList()
        {
            string strLog = Common.RunShell(PathLaunchctl, "list");
            return Common.GetDataByIndex(strLog, 2);
        }

        public static Dictionary<int, string> GetRunningServices()
        {
            Dictionary<int, string> dict = new Dictionary<int, string>();

            string strLog = Common.RunShell(PathLaunchctl, "list");
            List<string[]> listRows = Common.FormatData(strLog, -1, true);
            foreach (string[] arrItem in listRows)
            {
                int pid = Common.GetInteger(arrItem[0]);
                if (pid <= 0)
                    continue;

                dict[pid] = arrItem[2];
            }

            return dict;
        }

        public static Dictionary<int, string> GetRunningProcess(string includeProcessName = "")
        {
            Dictionary<int, string> dict = new Dictionary<int, string>();

            /*
  PID TTY           TIME CMD
    1 ??         0:39.91 /sbin/launchd
  120 ??         0:03.67 /usr/sbin/syslogd
             * */
            string strLog = Common.RunShell("/bin/ps", "-ax");
            int maxColumnCount = 4;
            List<string[]> listRows = Common.FormatData(strLog, maxColumnCount, true, includeProcessName);
            foreach (string[] arrItem in listRows)
            {
                if (arrItem.Length < 4)
                    continue;

                int pid = Common.GetInteger(arrItem[0]);
                if (pid <= 0)
                    continue;

                dict[pid] = arrItem[maxColumnCount - 1];
            }

            return dict;
        }

        public static Dictionary<int, string> GetProcess(string includeKeyword = "/")
        {
            Dictionary<int, string> dict = new Dictionary<int, string>();

            /*
 1218 ??         1:13.42 /Library/Frameworks/Mono.framework/Versions/6.0.0/bin/mono64 --debug /Users/<USER>/Library/Caches/VisualStudio/8.0/MSBuild/1047_1/MonoDevelop.MSBuildBuilder.exe 49440 False
 9022 ??         0:00.03 /usr/bin/sysdiagnose

             * */
            string strLog = Common.RunShell("/usr/bin/pgrep", "-f", "-l", includeKeyword);
            int maxColumnCount = 2;
            List<string[]> listRows = Common.FormatData(strLog, maxColumnCount, false);
            foreach (string[] arrItem in listRows)
            {
                if (arrItem.Length < maxColumnCount)
                    continue;

                int pid = Common.GetInteger(arrItem[0]);
                if (pid <= 0)
                    continue;

                dict[pid] = arrItem[maxColumnCount - 1];
            }

            return dict;
        }

        public static int GetProcessPID(string processPath, string paraArg = "")
        {
            int exePID = 0;

            int currentPID = Process.GetCurrentProcess().Id;

            string exePath = string.Empty;


            Dictionary<int, string> dict = GetRunningProcess(processPath);
            foreach (KeyValuePair<int, string> item in dict)
            {
                if (item.Key == currentPID)
                    continue;

                if (string.IsNullOrEmpty(paraArg) || item.Value.Contains(paraArg))
                {
                    exePID = item.Key;
                    exePath = item.Value;
                    break;
                }
            }

            ServiceMgr.WriteLine(string.Format("GetProcessPID -> ProcessPath = {0}, Path = {1}, PID = {2}, currentPID = {3}", processPath, exePath, exePID, currentPID));

            return exePID;
        }

        public static int GetCurrentAppProcessPID(string cmd)
        {
            string processName = Application.ExecutablePath;
            return GetProcessPID(processName, cmd);
        }

        public static bool CheckCurrentProcessExist(string cmd)
        {
            int pid = GetCurrentAppProcessPID(cmd);
            bool blnResult = pid > 0;
            MyLog.WriteLine($"[ServiceMgr.CheckCurrentProcessExist] 检查进程：cmd={cmd}, pid={pid}, exist={blnResult}");

            return blnResult;
        }

        public static void KillCurrentAppContainKey(string cmd)
        {
            int currentPID = Process.GetCurrentProcess().Id;

            string processName = Path.GetFileName(Application.ExecutablePath);

            Dictionary<int, string> dict = GetRunningProcess(processName);
            foreach (KeyValuePair<int, string> item in dict)
            {
                if (item.Key == currentPID)
                    continue;

                if (item.Value.Contains(cmd))
                {
                    ServiceMgr.WriteLine(string.Format("KillCurrentAppContainKey -> ProcessName = {0}, Path = {1}, PID = {2}, currentPID = {3}", processName, item.Value, item.Key, currentPID));
                    SocketMgr.KillProcessById(item.Key);
                }
            }
        }

        public static void KillCurrentExistApp()
        {
            int currentPID = Process.GetCurrentProcess().Id;

            string processName = Path.GetFileName(Application.ExecutablePath);

            Dictionary<int, string> dict = GetRunningProcess(processName);
            foreach (KeyValuePair<int, string> item in dict)
            {
                if (item.Key == currentPID)
                    continue;

                ScriptAppendKillPid(item.Key);
            }
        }

        public static void LaunchctlRestart(string serviceName)
        {
            MyLog.WriteLine($"[ServiceMgr.LaunchctlRestart] 重启服务：{serviceName}");
            string result = Common.RunShell(PathLaunchctl, "kickstart", "-k", serviceName);
            MyLog.WriteLine($"[ServiceMgr.LaunchctlRestart] 重启结果：{result}");
        }

        /// <summary>
        /// 专门针对macOS 10.11/10.12的服务强制启动方法
        /// </summary>
        public static bool ForceStartServiceForOldMacOS(string serviceName)
        {
            MyLog.WriteLine($"[ServiceMgr.ForceStartServiceForOldMacOS] 开始强制启动服务：{serviceName}");

            try
            {
                var osVersion = Common.OSVersion;
                if (osVersion >= new Version("10.13"))
                {
                    MyLog.WriteLine("[ServiceMgr.ForceStartServiceForOldMacOS] 不是旧版macOS，跳过");
                    return false;
                }

                string plistPath = GetServicePlistPath(serviceName);
                if (string.IsNullOrEmpty(plistPath))
                {
                    MyLog.WriteLine($"[ServiceMgr.ForceStartServiceForOldMacOS] 找不到plist文件：{serviceName}");
                    return false;
                }

                MyLog.WriteLine($"[ServiceMgr.ForceStartServiceForOldMacOS] plist路径：{plistPath}");

                // 验证plist文件是否存在和有效
                if (!File.Exists(plistPath))
                {
                    MyLog.WriteLine($"[ServiceMgr.ForceStartServiceForOldMacOS] plist文件不存在：{plistPath}");
                    return false;
                }

                // 验证plist文件格式
                try
                {
                    string plistContent = File.ReadAllText(plistPath);
                    MyLog.WriteLine($"[ServiceMgr.ForceStartServiceForOldMacOS] plist文件内容长度：{plistContent.Length}");

                    // 检查是否包含基本的XML结构
                    if (!plistContent.Contains("<?xml") || !plistContent.Contains("<plist") || !plistContent.Contains("</plist>"))
                    {
                        MyLog.WriteLine("[ServiceMgr.ForceStartServiceForOldMacOS] plist文件格式无效，缺少基本XML结构");
                        return false;
                    }

                    // 使用plutil验证plist格式
                    string validateResult = Common.RunShell("/usr/bin/plutil", "-lint", plistPath);
                    MyLog.WriteLine($"[ServiceMgr.ForceStartServiceForOldMacOS] plutil验证结果：{validateResult}");

                    if (!validateResult.Contains("OK"))
                    {
                        MyLog.WriteLine("[ServiceMgr.ForceStartServiceForOldMacOS] plist文件格式验证失败，尝试重新生成");
                        bool regenerated = RegeneratePlistForOldMacOS(serviceName);
                        if (!regenerated)
                        {
                            MyLog.WriteLine("[ServiceMgr.ForceStartServiceForOldMacOS] plist文件重新生成失败");
                            return false;
                        }
                        MyLog.WriteLine("[ServiceMgr.ForceStartServiceForOldMacOS] plist文件重新生成成功");
                    }
                }
                catch (Exception ex)
                {
                    MyLog.WriteLine($"[ServiceMgr.ForceStartServiceForOldMacOS] plist文件验证异常：{ex.Message}");
                    return false;
                }

                // 1. 先停止服务
                string result = Common.RunShell(PathLaunchctl, "stop", serviceName);
                MyLog.WriteLine($"[ServiceMgr.ForceStartServiceForOldMacOS] stop结果：{result}");

                // 2. unload服务
                result = Common.RunShell(PathLaunchctl, "unload", plistPath);
                MyLog.WriteLine($"[ServiceMgr.ForceStartServiceForOldMacOS] unload结果：{result}");

                // 3. 等待一下
                System.Threading.Thread.Sleep(2000);

                // 4. 重新load服务
                result = Common.RunShell(PathLaunchctl, "load", plistPath);
                MyLog.WriteLine($"[ServiceMgr.ForceStartServiceForOldMacOS] load结果：{result}");

                // 5. 等待加载完成
                System.Threading.Thread.Sleep(2000);

                // 6. 启动服务
                result = Common.RunShell(PathLaunchctl, "start", serviceName);
                MyLog.WriteLine($"[ServiceMgr.ForceStartServiceForOldMacOS] start结果：{result}");

                // 7. 验证启动结果
                System.Threading.Thread.Sleep(3000);
                var runningServices = GetRunningServices();
                bool isRunning = runningServices.Values.Contains(serviceName);
                MyLog.WriteLine($"[ServiceMgr.ForceStartServiceForOldMacOS] 最终验证：{serviceName} isRunning={isRunning}");

                return isRunning;
            }
            catch (Exception ex)
            {
                MyLog.WriteLine($"[ServiceMgr.ForceStartServiceForOldMacOS] 异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 重新生成并安装plist文件，专门针对macOS 10.11/10.12
        /// </summary>
        public static bool RegeneratePlistForOldMacOS(string serviceName)
        {
            MyLog.WriteLine($"[ServiceMgr.RegeneratePlistForOldMacOS] 开始重新生成plist：{serviceName}");

            try
            {
                string plistContent = "";
                bool isLaunchDaemon = false;

                // 根据服务名生成对应的plist内容
                switch (serviceName)
                {
                    case "com.sandstudio.remotesupport.session":
                        plistContent = CreatePlistAsSession();
                        isLaunchDaemon = true;
                        break;
                    case "com.sandstudio.remotesupport.service":
                        plistContent = CreatePlistAsService();
                        isLaunchDaemon = true;
                        break;
                    default:
                        MyLog.WriteLine($"[ServiceMgr.RegeneratePlistForOldMacOS] 不支持的服务名：{serviceName}");
                        return false;
                }

                string plistDir = isLaunchDaemon ? PathLaunchDaemons : PathLaunchAgents;
                string plistPath = Path.Combine(plistDir, $"{serviceName}.plist");

                MyLog.WriteLine($"[ServiceMgr.RegeneratePlistForOldMacOS] 生成plist路径：{plistPath}");

                // 删除旧的plist文件
                if (File.Exists(plistPath))
                {
                    File.Delete(plistPath);
                    MyLog.WriteLine("[ServiceMgr.RegeneratePlistForOldMacOS] 已删除旧plist文件");
                }

                // 写入新的plist文件
                File.WriteAllText(plistPath, plistContent, System.Text.Encoding.UTF8);
                MyLog.WriteLine("[ServiceMgr.RegeneratePlistForOldMacOS] 已写入新plist文件");

                // 设置权限
                Common.Chmod(plistPath, 644);
                MyLog.WriteLine("[ServiceMgr.RegeneratePlistForOldMacOS] 已设置plist文件权限");

                // 验证生成的plist文件
                string validateResult = Common.RunShell("/usr/bin/plutil", "-lint", plistPath);
                MyLog.WriteLine($"[ServiceMgr.RegeneratePlistForOldMacOS] 新plist验证结果：{validateResult}");

                return validateResult.Contains("OK");
            }
            catch (Exception ex)
            {
                MyLog.WriteLine($"[ServiceMgr.RegeneratePlistForOldMacOS] 异常：{ex.Message}");
                return false;
            }
        }

        public static void ExitAppForRS()
        {
            ExitAppForRS(true);
        }

        public static void ExitAppForRS(bool dockerExit)
        {
            MyLog.WriteLine("OnExitApp -> Kill Process");

            //隐藏Dock图标
            Application.SetActivationPolicy(NSApplicationActivationPolicy.Prohibited);

            //隐藏状态栏图标
            if (skStatusItem.Current != null)
                skStatusItem.Current.Visible = false;

            //通知Service用户主动退出
            if (SocketMgr.SendMsgFromClient(RSEventType.ExitApp))
            {
                //通过Socket发送退出消息失败，尝试直接Kill进程
                SocketMgr.KillProcessByName(Path.GetFileNameWithoutExtension(ServiceMgr.ServicePath), false);
            }
        }

        public static string ToArgPathFormat(string strArg)
        {
            if (string.IsNullOrEmpty(strArg))
                return string.Empty;
            else
                return strArg.Replace(@"""", @"\""");
        }

        public static string ConvertToPlistString(NSObject nsValue)
        {
            string strPlist = string.Empty;

            string tmpFile = Folder.GetTempFilePath(".plist");
            object objValue = iTong.Device.CoreFoundation.ManagedTypeFromCFType(nsValue.Handle);
            if (objValue != null)
                if (iTong.Device.CoreFoundation.WritePlist(objValue, tmpFile))
                    strPlist = iTong.Device.CoreFoundation.ReadPlist(tmpFile);

            return strPlist;
        }


        public static string RunAppleScript(string strScript)
        {
            StringBuilder sbLog = new StringBuilder();

            if (string.IsNullOrEmpty(strScript))
                goto DoExit;

            string[] arrLine = strScript.Split(new string[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries);
            if (arrLine.Length == 0)
                goto DoExit;

            StringBuilder sb = new StringBuilder();
            foreach (string strLine in arrLine)
            {
                sb.AppendFormat("{0};", ToArgPathFormat(strLine));
            }

            string command = string.Format("do shell script \"{0}\" with prompt \"{1} want to install daemon and agent\" with administrator privileges", sb.ToString(), ServiceMgr.ServiceNameForRS);
            sbLog.AppendLine(command);

            string strLog = Common.RunShell("/usr/bin/osascript", "-e", command);
            sbLog.AppendLine(strLog);

            //下面脚本会导致当前程序的进程卡住无法结束
            {
                //NSDictionary dictErrorReturn = null;
                //NSAppleScript appleScript = null;

                //try
                //{
                //    appleScript = new NSAppleScript(command);
                //    NSAppleEventDescriptor descriptor = appleScript.ExecuteAndReturnError(out dictErrorReturn);
                //    if (descriptor != null)
                //        sbLog.AppendLine(descriptor.StringValue + "");
                //}
                //catch (Exception ex)
                //{
                //    WriteLine(ex.ToString());
                //}
                //finally
                //{
                //    if (dictErrorReturn != null)
                //    {
                //        sbLog.AppendLine(ConvertToPlistString(dictErrorReturn));
                //        dictErrorReturn.Dispose();
                //    }

                //    if (appleScript != null)
                //        appleScript.Dispose();
                //}
            }

        DoExit:
            WriteLine(sbLog.ToString());

            return sbLog.ToString();
        }

        public static void WriteLine(string msg, string prefixName = "Shell")
        {
            Common.WriteLine(msg, prefixName, true);
        }

        public static string CreatePlistFile(string strPlist)
        {
            string pathPlist = Folder.GetTempFilePath(".plist");
            File.WriteAllText(pathPlist, strPlist, Encoding.UTF8);

            return pathPlist;
        }

        public static string GetServicePathByName(string serviceName, bool isLaunchDaemon)
        {
            string dirLaunch = isLaunchDaemon ? PathLaunchDaemons : PathLaunchAgents;
            string plistPath = Path.Combine(dirLaunch, string.Format("{0}.plist", serviceName));

            string plistPathFormat = plistPath.ToArgPath();

            return plistPathFormat;
        }

        public static string CreatePlistAsService(string pathExcute = "")
        {
            string strPlist = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<!DOCTYPE plist PUBLIC ""-//Apple//DTD PLIST 1.0//EN"" ""http://www.apple.com/DTDs/PropertyList-1.0.dtd"" >
<plist version = ""1.0"" >
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>StandardErrorPath</key>
	<string>/var/log/AirDroidRemoteSupport</string>
	<key>StandardOutPath</key>
	<string>/var/log/AirDroidRemoteSupport</string>
	<key>KeepAlive</key>
	<{2}/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.service</string>
	<key>ProgramArguments</key>
	<array>
		<string>{0}</string>
		<string>t=runService</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>{1}</string>
</dict>
</plist>
";

            string logFolder = Folder.LogFolder;

            /*、
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>AssociatedBundleIdentifiers</key>
    <array>
        <string>com.sandstudio.remotesupport</string>
    </array>
    <key>Disabled</key>
    <false/>
    <key>KeepAlive</key>
    <true/>
    <key>Label</key>
    <string>com.sandstudio.remotesupport.service</string>
    <key>ProgramArguments</key>
    <array>
        <string>/bin/sh</string>
        <string>-c</string>
        <string>sleep 3; if ([ -f "/Library/Caches/AirDroidRemoteSupport/Service.dll" ] || pgrep -f 'RemoteSupport t=runWindows' &gt; /dev/null) &amp;&amp; ! pgrep -f 'RemoteSupport t=runService' &gt; /dev/null; then launchctl unload /Library/LaunchAgents/com.sandstudio.remotesupport.session.plist; launchctl stop com.sandstudio.remotesupport.session; launchctl load /Library/LaunchAgents/com.sandstudio.remotesupport.session.plist; launchctl start com.sandstudio.remotesupport.session; fi</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>WorkingDirectory</key>
    <string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist> 
             */
            string cmd = string.Format("sleep 3; if ([ -f \"/Library/Caches/AirDroidRemoteSupport/Service.dll\" ] || pgrep -f '{0} {1}' > /dev/null) && ! pgrep -f '{0} {2}' > /dev/null; then launchctl unload {4}; launchctl stop {3}; launchctl load {4}; launchctl start {3}; fi",
                Path.GetFileName(Application.ExecutablePath),
                ParaMgr.CommandRunWidnows,
                ParaMgr.CommandRunService,
                ServiceNameForRS_Session,
                ServiceMgr.GetServicePathByName(ServiceNameForRS_Session, true));

            string exePath = ServicePathForRS;
            if (!File.Exists(exePath))
                exePath = Application.ExecutablePath;

            LaunchArgs args = new LaunchArgs()
            {
                Label = ServiceNameForRS_Service,
                ProgramArguments = new List<string> { "/bin/sh", "-c", cmd },
                AssociatedBundleIdentifiers = ServiceNameForRS_Host,
                Disabled = false,
                KeepAlive = exePath.StartsWith("/Applications/", StringComparison.OrdinalIgnoreCase),
                RunAtLoad = true,
                WorkingDirectory = Folder.MacOSFolder,

                //暂不设置日志目录，否则会导致服务启动异常
                //StandardOutPath = Path.Combine(Folder.LogFolder, "Out.log"),
                //StandardErrorPath = Path.Combine(Folder.LogFolder, "Error.log"),
            };

            return CreatePlist(args, false);
        }

        public static string CreatePlistAsSession(string pathExcute = "")
        {
            MyLog.WriteLine("[ServiceMgr.CreatePlistAsSession] 开始创建Session plist");

            string exePath = ServicePathForRS;
            if (!File.Exists(exePath))
                exePath = Application.ExecutablePath;

            MyLog.WriteLine($"[ServiceMgr.CreatePlistAsSession] 使用可执行文件路径：{exePath}");

            // 检查macOS版本，为旧版本设置更合适的参数
            bool isOldMacOS = false;
            try
            {
                var osVersion = Common.OSVersion;
                isOldMacOS = osVersion < new Version("10.13");
            }
            catch
            {
                isOldMacOS = true;
            }

            LaunchArgs args = new LaunchArgs()
            {
                Label = ServiceNameForRS_Session,
                ProgramArguments = new List<string> { exePath, ParaMgr.CommandRunService },
                AssociatedBundleIdentifiers = ServiceNameForRS_Host,
                Disabled = false,
                KeepAlive = isOldMacOS ? true : false,  // 旧版macOS使用KeepAlive=true
                RunAtLoad = isOldMacOS ? true : false,  // 旧版macOS使用RunAtLoad=true
                WorkingDirectory = Folder.MacOSFolder,

                //暂不设置日志目录，否则会导致服务启动异常
                //StandardOutPath = Path.Combine(Folder.LogFolder, "Out.log"),
                //StandardErrorPath = Path.Combine(Folder.LogFolder, "Error.log"),
            };

            return CreatePlist(args, false);
        }

        public static string CreatePlistAsProxy()
        {
            string strPlist = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<!DOCTYPE plist PUBLIC ""-//Apple//DTD PLIST 1.0//EN"" ""http://www.apple.com/DTDs/PropertyList-1.0.dtd"" >
<plist version = ""1.0"" >
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<true/>
	<key>KeepAlive</key>
	<false/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.proxy</string>
	<key>LimitLoadToSessionType</key>
	<array>
		<string>LoginWindow</string>
		<string>Aqua</string>
	</array>
	<key>ProcessType</key>
	<string>Interactive</string>
	<key>ProgramArguments</key>
	<array>
		<string>{0}</string>
		<string>t=runVideo</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>{1}</string>
</dict>
</plist>
";
            string exePath = ServicePathForRS;
            if (!File.Exists(exePath))
                exePath = Application.ExecutablePath;

            LaunchArgs args = new LaunchArgs()
            {
                Label = ServiceNameForRS_Proxy,
                LimitLoadToSessionType = LimitLoadToSessionType.LoginWindow,
                ProgramArguments = new List<string> { exePath, ParaMgr.CommandRunProxy },
                AssociatedBundleIdentifiers = ServiceNameForRS_Host,
                //Disabled = true,
                KeepAlive = exePath.StartsWith("/Applications/", StringComparison.OrdinalIgnoreCase),
                RunAtLoad = true,
                WorkingDirectory = Folder.MacOSFolder,
            };

            return CreatePlist(args, false);
        }

        public static string CreatePlistAsVideo()
        {
            string strPlist = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<!DOCTYPE plist PUBLIC ""-//Apple//DTD PLIST 1.0//EN"" ""http://www.apple.com/DTDs/PropertyList-1.0.dtd"" >
<plist version = ""1.0"" >
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<true/>
	<key>KeepAlive</key>
	<false/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.video</string>
	<key>LimitLoadToSessionType</key>
	<array>
		<string>LoginWindow</string>
		<string>Aqua</string>
	</array>
	<key>ProcessType</key>
	<string>Interactive</string>
	<key>ProgramArguments</key>
	<array>
		<string>{0}</string>
		<string>t=runVideo</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>{1}</string>
</dict>
</plist>
";
            string exePath = ServicePathForRS;
            if (!File.Exists(exePath))
                exePath = Application.ExecutablePath;

            LaunchArgs args = new LaunchArgs()
            {
                Label = ServiceNameForRS_Video,
                LimitLoadToSessionType = LimitLoadToSessionType.Aqua | LimitLoadToSessionType.LoginWindow,
                ProgramArguments = new List<string> { exePath, ParaMgr.CommandRunVideo },
                AssociatedBundleIdentifiers = ServiceNameForRS_Host,
                //Disabled = true,
                KeepAlive = false,
                RunAtLoad = true,
                WorkingDirectory = Folder.MacOSFolder,
            };

            return CreatePlist(args, false);
        }

        public static string CreatePlistAsWindows()
        {
            string strPlist = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<!DOCTYPE plist PUBLIC ""-//Apple//DTD PLIST 1.0//EN"" ""http://www.apple.com/DTDs/PropertyList-1.0.dtd"" >
<plist version = ""1.0"" >
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<false/>
	<key>KeepAlive</key>
	<false/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.windows</string>
	<key>LimitLoadToSessionType</key>
	<array>
		<string>Aqua</string>
	</array>
	<key>ProgramArguments</key>
	<array>
		<string>{0}</string>
		<string>t=runWindows</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>{1}</string>
</dict>
</plist>
";

            LaunchArgs args = new LaunchArgs()
            {
                Label = ServiceNameForRS_Windows,
                LimitLoadToSessionType = LimitLoadToSessionType.Aqua,
                ProgramArguments = new List<string> { ParaMgr.CommandRunWidnows },
                AssociatedBundleIdentifiers = ServiceNameForRS_Host,
                Disabled = false,
                KeepAlive = false,
                RunAtLoad = true,
                WorkingDirectory = Folder.MacOSFolder,
            };

            return CreatePlist(args);
        }

        public static string CreatePlistAsSafeMode()
        {
            string strPlist = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<!DOCTYPE plist PUBLIC ""-//Apple//DTD PLIST 1.0//EN"" ""http://www.apple.com/DTDs/PropertyList-1.0.dtd"" >
<plist version = ""1.0"" >
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<false/>
	<key>KeepAlive</key>
	<false/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.safemode</string>
	<key>LimitLoadToSessionType</key>
	<array>
		<string>Aqua</string>
	</array>
	<key>ProgramArguments</key>
	<array>
		<string>{0}</string>
		<string>t=runSafeMode</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>{1}</string>
</dict>
</plist>
";

            string exePath = ServicePathForRS;
            if (!File.Exists(exePath))
                exePath = Application.ExecutablePath;

            LaunchArgs args = new LaunchArgs()
            {
                Label = ServiceNameForRS_SafeMode,
                LimitLoadToSessionType = LimitLoadToSessionType.Aqua,
                ProgramArguments = new List<string> { exePath, ParaMgr.CommandRunSafeMode },
                AssociatedBundleIdentifiers = ServiceNameForRS_Host,
                Disabled = false,
                KeepAlive = false,
                RunAtLoad = true,
                WorkingDirectory = Folder.MacOSFolder,
            };

            return CreatePlist(args, false);
        }

        public static string CreatePlist(LaunchArgs args, bool appendExePath = true, string cmdFormat = "")
        {
            if (args.AssociatedBundleIdentifiersList == null)
                args.AssociatedBundleIdentifiersList = new List<string>();

            if (args.ProgramArguments == null)
                args.ProgramArguments = new List<string>();

            if (string.IsNullOrEmpty(args.WorkingDirectory))
                args.WorkingDirectory = Folder.MacOSFolder;

            if (string.IsNullOrEmpty(args.Program))
                args.Program = Application.ExecutablePath;

            if (args.AssociatedBundleIdentifiersList.Count == 0)
            {
                if (string.IsNullOrEmpty(args.AssociatedBundleIdentifiers))
                    args.AssociatedBundleIdentifiers = ServiceNameForRS_Host;

                args.AssociatedBundleIdentifiersList.Add(args.AssociatedBundleIdentifiers);
            }

            Dictionary<object, object> dictPlist = new Dictionary<object, object>();

            // 检查macOS版本，为旧版本移除不兼容的键
            bool isOldMacOS = false;
            try
            {
                var osVersion = Common.OSVersion;
                isOldMacOS = osVersion < new Version("10.13");
                MyLog.WriteLine($"[ServiceMgr.CreatePlist] macOS版本：{osVersion}, isOldMacOS={isOldMacOS}");
            }
            catch (Exception ex)
            {
                // 如果无法获取版本，默认使用兼容模式
                isOldMacOS = true;
                MyLog.WriteLine($"[ServiceMgr.CreatePlist] 无法获取macOS版本，使用兼容模式：{ex.Message}");
            }

            // 只在新版本macOS中添加AssociatedBundleIdentifiers
            if (!isOldMacOS)
            {
                dictPlist["AssociatedBundleIdentifiers"] = args.AssociatedBundleIdentifiersList;
                MyLog.WriteLine("[ServiceMgr.CreatePlist] 添加AssociatedBundleIdentifiers（新版macOS）");
            }
            else
            {
                MyLog.WriteLine("[ServiceMgr.CreatePlist] 跳过AssociatedBundleIdentifiers（旧版macOS兼容）");
                MyLog.WriteLine($"[ServiceMgr.CreatePlist] 强制验证：dictPlist包含AssociatedBundleIdentifiers={dictPlist.ContainsKey("AssociatedBundleIdentifiers")}");
            }

            if (args.Disabled != null)
                // 在旧版macOS中跳过Disabled键，可能导致兼容性问题
            if (!isOldMacOS)
            {
                dictPlist["Disabled"] = args.Disabled;
                MyLog.WriteLine("[ServiceMgr.CreatePlist] 添加Disabled键（新版macOS）");
            }
            else
            {
                MyLog.WriteLine("[ServiceMgr.CreatePlist] 跳过Disabled键（旧版macOS兼容）");
            }

            dictPlist["KeepAlive"] = args.KeepAlive;
            dictPlist["Label"] = args.Label;

            if (args.LimitLoadToSessionType != LimitLoadToSessionType.None)
                dictPlist["LimitLoadToSessionType"] = args.GetLimitLoadToSessionType();


            if (args.ProgramArguments.Count > 0)
            {
                //程序路径不在参数列表中，将程序路径插入在第一个参数列表
                if (appendExePath && !string.IsNullOrEmpty(args.Program) && !args.ProgramArguments.Contains(args.Program))
                    args.ProgramArguments.Insert(0, args.Program);

                dictPlist["ProgramArguments"] = args.ProgramArguments;
            }
            else if (!string.IsNullOrEmpty(args.Program))
            {
                dictPlist["Program"] = args.Program;
            }

            dictPlist["RunAtLoad"] = args.RunAtLoad;

            if (args.LaunchOnlyOnce != null)
                dictPlist["LaunchOnlyOnce"] = args.LaunchOnlyOnce;

            if (args.OnDemand != null)
                dictPlist["OnDemand"] = args.OnDemand;

            if (!string.IsNullOrEmpty(args.WorkingDirectory))
                dictPlist["WorkingDirectory"] = args.WorkingDirectory;

            if (!string.IsNullOrEmpty(args.StandardOutPath))
                dictPlist["StandardOutPath"] = args.StandardOutPath;

            if (!string.IsNullOrEmpty(args.StandardErrorPath))
                dictPlist["StandardErrorPath"] = args.StandardErrorPath;

            if (!string.IsNullOrEmpty(args.EnvironmentVariables))
                dictPlist["EnvironmentVariables"] = args.EnvironmentVariables;

            if (!string.IsNullOrEmpty(args.UserName))
                dictPlist["UserName"] = args.UserName;

            if (!string.IsNullOrEmpty(args.GroupName))
                dictPlist["GroupName"] = args.GroupName;

            if (!string.IsNullOrEmpty(args.ProcessType))
                dictPlist["ProcessType"] = args.ProcessType;

            if (args.StartCalendarInterval != null)
            {
                object objValue = args.StartCalendarInterval.GetValue();
                if (objValue != null)
                    dictPlist["StartCalendarInterval"] = objValue;
            }

            if (args.MachServices != null && args.MachServices.Count > 0)
                dictPlist["MachServices"] = args.MachServices;

            if (args.Sockets != null && args.Sockets.Count > 0)
                dictPlist["Sockets"] = args.Sockets;

            string strPlist = iTong.Device.CoreFoundation.CreatePlistString(dictPlist);
            MyLog.WriteLine($"[ServiceMgr.CreatePlist] 生成的plist内容长度：{strPlist?.Length ?? 0}");

            if (!string.IsNullOrEmpty(cmdFormat))
            {
                strPlist = string.Format(strPlist, cmdFormat);
                MyLog.WriteLine($"[ServiceMgr.CreatePlist] 应用cmdFormat后的plist长度：{strPlist?.Length ?? 0}");
            }

            // 验证生成的plist是否包含AssociatedBundleIdentifiers
            if (strPlist.Contains("AssociatedBundleIdentifiers"))
            {
                MyLog.WriteLine("[ServiceMgr.CreatePlist] 警告：生成的plist仍然包含AssociatedBundleIdentifiers！");
            }
            else
            {
                MyLog.WriteLine("[ServiceMgr.CreatePlist] 确认：生成的plist不包含AssociatedBundleIdentifiers");
            }

            string plistFilePath = CreatePlistFile(strPlist);
            MyLog.WriteLine($"[ServiceMgr.CreatePlist] 创建临时plist文件：{plistFilePath}");

            return plistFilePath;
        }

        /// <summary>判断当前用户是否是管理员（admin组）</summary>
        /// <returns></returns>
        public static bool IsCurrentUserAdmin()
        {
            try
            {
                string userName = GetConsoleUserName();

                if (string.IsNullOrEmpty(userName) || userName == "loginwindow")
                {
                    MyLog.WriteLine($"[ServiceMgr.IsCurrentUserAdminByShell] userName为空或为loginwindow，返回false");
                    return false;
                }

                string groupList = Common.RunShell("/usr/bin/id", "-Gn", userName);
                MyLog.WriteLine($"[ServiceMgr.IsCurrentUserAdminByShell] userName={userName}, groupList={groupList}");

                if (!string.IsNullOrEmpty(groupList) && groupList.Contains("admin"))
                    return true;
                else
                    return false;
            }
            catch (Exception ex)
            {
                MyLog.WriteLine($"[ServiceMgr.IsCurrentUserAdminByShell] Exception: {ex}");
                return false;
            }
        }
    }
}