#!/bin/bash

echo "=== 分析代码生成的plist文件 ==="
echo "测试时间: $(date)"
echo ""

echo "1. 清理环境..."
sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist 2>/dev/null
sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist 2>/dev/null
sudo rm -f /Library/LaunchDaemons/com.sandstudio.remotesupport.*.plist
sudo rm -rf /tmp/AirDroidRemoteSupport/
echo "✅ 环境已清理"

echo ""
echo "2. 创建成功的手动plist作为对比基准..."
cat > /tmp/manual_success.plist << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>KeepAlive</key>
	<true/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.session</string>
	<key>ProgramArguments</key>
	<array>
		<string>/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport</string>
		<string>t=runService</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist>
EOF
echo "✅ 对比基准plist已创建"

echo ""
echo "3. 运行RemoteSupport服务安装..."
echo "请注意观察安装过程中的任何错误信息..."
sudo /Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport

echo ""
echo "4. 查看临时生成的plist文件..."
if [ -d "/tmp/AirDroidRemoteSupport/" ]; then
    echo "临时目录存在，查看所有plist文件："
    find /tmp/AirDroidRemoteSupport/ -name "*.plist" -exec echo "=== {} ===" \; -exec cat {} \; -exec echo "" \;
else
    echo "❌ 临时目录不存在"
fi

echo ""
echo "5. 查看安装到系统的plist文件..."
if [ -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist" ]; then
    echo "=== Session plist 内容 ==="
    cat /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
    echo ""
else
    echo "❌ Session plist文件不存在"
fi

if [ -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist" ]; then
    echo "=== Service plist 内容 ==="
    cat /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist
    echo ""
else
    echo "❌ Service plist文件不存在"
fi

echo ""
echo "6. 文件属性对比..."
if [ -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist" ]; then
    echo "=== 文件大小和权限对比 ==="
    echo "代码生成的plist:"
    ls -la /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
    echo "手动创建的plist:"
    ls -la /tmp/manual_success.plist
    
    echo ""
    echo "=== 编码检查 ==="
    echo "代码生成的plist编码:"
    file /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
    echo "手动创建的plist编码:"
    file /tmp/manual_success.plist
    
    echo ""
    echo "=== 内容差异分析 ==="
    diff /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist /tmp/manual_success.plist
    
    if [ $? -eq 0 ]; then
        echo "✅ 文件内容完全相同"
    else
        echo "❌ 文件内容有差异"
        
        echo ""
        echo "=== 二进制对比（前200字节） ==="
        echo "代码生成的plist:"
        hexdump -C /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist | head -10
        echo ""
        echo "手动创建的plist:"
        hexdump -C /tmp/manual_success.plist | head -10
    fi
else
    echo "❌ 无法进行对比，代码生成的plist文件不存在"
fi

echo ""
echo "7. 验证plist格式..."
if [ -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist" ]; then
    echo "验证代码生成的plist:"
    plutil -lint /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
    
    echo "验证手动创建的plist:"
    plutil -lint /tmp/manual_success.plist
else
    echo "❌ 无法验证，代码生成的plist文件不存在"
fi

echo ""
echo "8. 尝试加载代码生成的plist..."
if [ -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist" ]; then
    LOAD_RESULT=$(sudo launchctl load /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist 2>&1)
    if [ $? -eq 0 ]; then
        echo "✅ 代码生成的plist加载成功"
        if [ -n "$LOAD_RESULT" ]; then
            echo "加载输出: $LOAD_RESULT"
        fi
    else
        echo "❌ 代码生成的plist加载失败"
        echo "错误信息: $LOAD_RESULT"
    fi
else
    echo "❌ 无法测试加载，代码生成的plist文件不存在"
fi

echo ""
echo "=== 分析完成 ==="
echo "测试结束时间: $(date)"
