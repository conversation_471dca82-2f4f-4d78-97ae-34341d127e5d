#!/bin/bash

echo "=== 检查ToDesk.plist文件编码 ==="
echo "检查时间: $(date)"
echo ""

TODESK_PLIST="/Users/<USER>/Project/GitCode/tongbu_assistant/AirDroidRSHost_Mac/zRS/ToDesk.plist"

echo "1. 检查文件是否存在..."
if [ -f "$TODESK_PLIST" ]; then
    echo "✅ 文件存在: $TODESK_PLIST"
else
    echo "❌ 文件不存在: $TODESK_PLIST"
    exit 1
fi

echo ""
echo "2. 使用file命令检查编码..."
file "$TODESK_PLIST"

echo ""
echo "3. 使用hexdump查看文件开头的字节..."
echo "前16字节:"
hexdump -C "$TODESK_PLIST" | head -1

echo ""
echo "4. 检查是否有BOM..."
# 检查前3个字节是否是 EF BB BF (UTF-8 BOM)
FIRST_BYTES=$(hexdump -C "$TODESK_PLIST" | head -1 | cut -d' ' -f2-4)
echo "文件前3个字节: $FIRST_BYTES"

if [[ "$FIRST_BYTES" == "ef bb bf" ]]; then
    echo "❌ 检测到UTF-8 BOM"
else
    echo "✅ 没有检测到UTF-8 BOM"
fi

echo ""
echo "5. 验证plist格式..."
plutil -lint "$TODESK_PLIST"

echo ""
echo "6. 对比我们之前的RemoteSupport plist..."
echo "RemoteSupport session plist编码:"
if [ -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist" ]; then
    file "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
    echo "前3个字节:"
    hexdump -C "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist" | head -1 | cut -d' ' -f2-4
else
    echo "RemoteSupport session plist不存在"
fi

echo ""
echo "=== 检查完成 ==="
