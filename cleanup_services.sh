#!/bin/bash

echo "=== RemoteSupport服务完全清理 ==="
echo "清理时间: $(date)"
echo ""

echo "1. 查看当前运行的RemoteSupport进程..."
ps aux | grep RemoteSupport | grep -v grep

echo ""
echo "2. 查看当前注册的RemoteSupport服务..."
sudo launchctl list | grep remotesupport

echo ""
echo "3. 停止所有RemoteSupport服务..."
sudo launchctl stop com.sandstudio.remotesupport.session 2>/dev/null
sudo launchctl stop com.sandstudio.remotesupport.service 2>/dev/null
sudo launchctl stop com.sandstudio.remotesupport.windows 2>/dev/null
sudo launchctl stop com.sandstudio.remotesupport.proxy 2>/dev/null
sudo launchctl stop com.sandstudio.remotesupport.video 2>/dev/null
sudo launchctl stop com.sandstudio.remotesupport.safemode 2>/dev/null
echo "✅ 服务停止命令已执行"

echo ""
echo "4. 卸载所有RemoteSupport服务..."
sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist 2>/dev/null
sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist 2>/dev/null
sudo launchctl unload /Library/LaunchAgents/com.sandstudio.remotesupport.windows.plist 2>/dev/null
sudo launchctl unload /Library/LaunchAgents/com.sandstudio.remotesupport.proxy.plist 2>/dev/null
sudo launchctl unload /Library/LaunchAgents/com.sandstudio.remotesupport.video.plist 2>/dev/null
sudo launchctl unload /Library/LaunchAgents/com.sandstudio.remotesupport.safemode.plist 2>/dev/null
echo "✅ 服务卸载命令已执行"

echo ""
echo "5. 删除所有plist文件..."
sudo rm -f /Library/LaunchDaemons/com.sandstudio.remotesupport.*.plist
sudo rm -f /Library/LaunchAgents/com.sandstudio.remotesupport.*.plist
echo "✅ plist文件已删除"

echo ""
echo "6. 等待3秒让服务完全停止..."
sleep 3

echo ""
echo "7. 强制终止所有RemoteSupport进程..."
sudo pkill -f "RemoteSupport"
sleep 2
sudo pkill -9 -f "RemoteSupport"
echo "✅ 进程终止命令已执行"

echo ""
echo "8. 清理临时文件..."
sudo rm -rf /tmp/AirDroidRemoteSupport/
echo "✅ 临时文件已清理"

echo ""
echo "9. 验证清理结果..."
echo "剩余的RemoteSupport进程:"
REMAINING_PROCESSES=$(ps aux | grep RemoteSupport | grep -v grep)
if [ -z "$REMAINING_PROCESSES" ]; then
    echo "✅ 没有剩余进程"
else
    echo "❌ 仍有进程运行:"
    echo "$REMAINING_PROCESSES"
fi

echo ""
echo "剩余的RemoteSupport服务:"
REMAINING_SERVICES=$(sudo launchctl list | grep remotesupport)
if [ -z "$REMAINING_SERVICES" ]; then
    echo "✅ 没有剩余服务"
else
    echo "❌ 仍有服务注册:"
    echo "$REMAINING_SERVICES"
fi

echo ""
echo "剩余的plist文件:"
REMAINING_PLISTS=$(find /Library/LaunchDaemons/ /Library/LaunchAgents/ -name "*remotesupport*" 2>/dev/null)
if [ -z "$REMAINING_PLISTS" ]; then
    echo "✅ 没有剩余plist文件"
else
    echo "❌ 仍有plist文件:"
    echo "$REMAINING_PLISTS"
fi

echo ""
if [ -z "$REMAINING_PROCESSES" ] && [ -z "$REMAINING_SERVICES" ] && [ -z "$REMAINING_PLISTS" ]; then
    echo "🎉 清理完成！所有RemoteSupport服务和进程已停止"
else
    echo "⚠️  清理不完全，可能需要手动处理剩余项目"
fi

echo ""
echo "清理结束时间: $(date)"
