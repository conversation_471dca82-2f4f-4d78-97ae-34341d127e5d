#!/bin/bash

echo "=== 修复plist文件的BOM问题 ==="
echo "修复时间: $(date)"
echo ""

# 定义plist文件路径
SESSION_PLIST="/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
SERVICE_PLIST="/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"

echo "1. 检查当前文件编码..."
echo "Session plist:"
sudo file "$SESSION_PLIST"
echo "Service plist:"
sudo file "$SERVICE_PLIST"
echo ""

echo "2. 备份原始文件..."
sudo cp "$SESSION_PLIST" "$SESSION_PLIST.backup"
sudo cp "$SERVICE_PLIST" "$SERVICE_PLIST.backup"
echo "✅ 备份完成"
echo ""

echo "3. 移除BOM并重新保存文件..."

# 移除session plist的BOM
echo "处理session plist..."
sudo sed '1s/^\xEF\xBB\xBF//' "$SESSION_PLIST" | sudo tee "$SESSION_PLIST.tmp" > /dev/null
sudo mv "$SESSION_PLIST.tmp" "$SESSION_PLIST"

# 移除service plist的BOM
echo "处理service plist..."
sudo sed '1s/^\xEF\xBB\xBF//' "$SERVICE_PLIST" | sudo tee "$SERVICE_PLIST.tmp" > /dev/null
sudo mv "$SERVICE_PLIST.tmp" "$SERVICE_PLIST"

echo "✅ BOM移除完成"
echo ""

echo "4. 验证修复后的文件编码..."
echo "Session plist:"
sudo file "$SESSION_PLIST"
echo "Service plist:"
sudo file "$SERVICE_PLIST"
echo ""

echo "5. 验证plist格式..."
echo "验证session plist:"
sudo plutil -lint "$SESSION_PLIST"
echo "验证service plist:"
sudo plutil -lint "$SERVICE_PLIST"
echo ""

echo "6. 卸载现有服务..."
sudo launchctl unload "$SESSION_PLIST" 2>/dev/null || true
sudo launchctl unload "$SERVICE_PLIST" 2>/dev/null || true
echo "✅ 服务卸载完成"
echo ""

echo "7. 重新加载修复后的服务..."
echo "加载session plist..."
SESSION_LOAD_RESULT=$(sudo launchctl load "$SESSION_PLIST" 2>&1)
if [ $? -eq 0 ]; then
    echo "✅ Session plist加载成功"
    if [ -n "$SESSION_LOAD_RESULT" ]; then
        echo "输出: $SESSION_LOAD_RESULT"
    fi
else
    echo "❌ Session plist加载失败"
    echo "错误: $SESSION_LOAD_RESULT"
fi

echo ""
echo "加载service plist..."
SERVICE_LOAD_RESULT=$(sudo launchctl load "$SERVICE_PLIST" 2>&1)
if [ $? -eq 0 ]; then
    echo "✅ Service plist加载成功"
    if [ -n "$SERVICE_LOAD_RESULT" ]; then
        echo "输出: $SERVICE_LOAD_RESULT"
    fi
else
    echo "❌ Service plist加载失败"
    echo "错误: $SERVICE_LOAD_RESULT"
fi

echo ""
echo "8. 检查服务状态..."
echo "当前注册的remotesupport服务:"
sudo launchctl list | grep remotesupport

echo ""
echo "当前运行的RemoteSupport进程:"
ps aux | grep RemoteSupport | grep -v grep

echo ""
echo "=== 修复完成 ==="
echo ""
echo "如果还有问题，可以使用以下命令恢复备份:"
echo "sudo cp $SESSION_PLIST.backup $SESSION_PLIST"
echo "sudo cp $SERVICE_PLIST.backup $SERVICE_PLIST"
