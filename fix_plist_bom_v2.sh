#!/bin/bash

echo "=== 修复plist文件的BOM问题 (强力版本) ==="
echo "修复时间: $(date)"
echo ""

# 定义plist文件路径
SESSION_PLIST="/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"
SERVICE_PLIST="/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"

echo "1. 检查当前文件编码..."
echo "Session plist:"
sudo file "$SESSION_PLIST"
echo "Service plist:"
sudo file "$SERVICE_PLIST"
echo ""

echo "2. 使用hexdump查看文件开头的字节..."
echo "Session plist前16字节:"
sudo hexdump -C "$SESSION_PLIST" | head -1
echo "Service plist前16字节:"
sudo hexdump -C "$SERVICE_PLIST" | head -1
echo ""

echo "3. 使用多种方法移除BOM..."

# 方法1: 使用tail跳过BOM
echo "方法1: 使用tail跳过BOM字节..."
sudo tail -c +4 "$SESSION_PLIST" | sudo tee "$SESSION_PLIST.nobom" > /dev/null
sudo tail -c +4 "$SERVICE_PLIST" | sudo tee "$SERVICE_PLIST.nobom" > /dev/null

# 方法2: 使用dd跳过BOM
echo "方法2: 使用dd跳过BOM字节..."
sudo dd if="$SESSION_PLIST" of="$SESSION_PLIST.nobom2" bs=1 skip=3 2>/dev/null
sudo dd if="$SERVICE_PLIST" of="$SERVICE_PLIST.nobom2" bs=1 skip=3 2>/dev/null

echo "✅ BOM移除完成"
echo ""

echo "4. 验证移除结果..."
echo "方法1结果:"
echo "Session plist (tail方法):"
sudo file "$SESSION_PLIST.nobom"
echo "Service plist (tail方法):"
sudo file "$SERVICE_PLIST.nobom"

echo ""
echo "方法2结果:"
echo "Session plist (dd方法):"
sudo file "$SESSION_PLIST.nobom2"
echo "Service plist (dd方法):"
sudo file "$SERVICE_PLIST.nobom2"
echo ""

echo "5. 验证plist格式..."
echo "验证tail方法的session plist:"
sudo plutil -lint "$SESSION_PLIST.nobom"
echo "验证tail方法的service plist:"
sudo plutil -lint "$SERVICE_PLIST.nobom"

echo ""
echo "验证dd方法的session plist:"
sudo plutil -lint "$SESSION_PLIST.nobom2"
echo "验证dd方法的service plist:"
sudo plutil -lint "$SERVICE_PLIST.nobom2"
echo ""

echo "6. 选择最佳方法并替换原文件..."
# 检查哪种方法效果更好
if sudo plutil -lint "$SESSION_PLIST.nobom" >/dev/null 2>&1; then
    echo "使用tail方法的结果..."
    sudo mv "$SESSION_PLIST.nobom" "$SESSION_PLIST"
    sudo mv "$SERVICE_PLIST.nobom" "$SERVICE_PLIST"
    # 清理dd方法的临时文件
    sudo rm -f "$SESSION_PLIST.nobom2" "$SERVICE_PLIST.nobom2"
elif sudo plutil -lint "$SESSION_PLIST.nobom2" >/dev/null 2>&1; then
    echo "使用dd方法的结果..."
    sudo mv "$SESSION_PLIST.nobom2" "$SESSION_PLIST"
    sudo mv "$SERVICE_PLIST.nobom2" "$SERVICE_PLIST"
    # 清理tail方法的临时文件
    sudo rm -f "$SESSION_PLIST.nobom" "$SERVICE_PLIST.nobom"
else
    echo "❌ 两种方法都失败了，恢复原文件"
    sudo rm -f "$SESSION_PLIST.nobom" "$SERVICE_PLIST.nobom"
    sudo rm -f "$SESSION_PLIST.nobom2" "$SERVICE_PLIST.nobom2"
    exit 1
fi

echo "✅ 文件替换完成"
echo ""

echo "7. 验证最终结果..."
echo "Session plist:"
sudo file "$SESSION_PLIST"
echo "Service plist:"
sudo file "$SERVICE_PLIST"
echo ""

echo "8. 测试launchctl加载..."
echo "卸载现有服务..."
sudo launchctl unload "$SESSION_PLIST" 2>/dev/null || true
sudo launchctl unload "$SERVICE_PLIST" 2>/dev/null || true

echo ""
echo "加载session plist..."
SESSION_LOAD_RESULT=$(sudo launchctl load "$SESSION_PLIST" 2>&1)
if [ $? -eq 0 ]; then
    echo "✅ Session plist加载成功"
    if [ -n "$SESSION_LOAD_RESULT" ]; then
        echo "输出: $SESSION_LOAD_RESULT"
    fi
else
    echo "❌ Session plist加载失败"
    echo "错误: $SESSION_LOAD_RESULT"
fi

echo ""
echo "加载service plist..."
SERVICE_LOAD_RESULT=$(sudo launchctl load "$SERVICE_PLIST" 2>&1)
if [ $? -eq 0 ]; then
    echo "✅ Service plist加载成功"
    if [ -n "$SERVICE_LOAD_RESULT" ]; then
        echo "输出: $SERVICE_LOAD_RESULT"
    fi
else
    echo "❌ Service plist加载失败"
    echo "错误: $SERVICE_LOAD_RESULT"
fi

echo ""
echo "9. 检查服务状态..."
echo "当前注册的remotesupport服务:"
sudo launchctl list | grep remotesupport

echo ""
echo "当前运行的RemoteSupport进程:"
ps aux | grep RemoteSupport | grep -v grep

echo ""
echo "=== 修复完成 ==="
