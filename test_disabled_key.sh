#!/bin/bash

echo "=== 测试Disabled键在macOS 10.12上的影响 ==="
echo "测试时间: $(date)"
echo ""

# 测试1：包含Disabled键的plist
echo "========== 测试1：包含Disabled键 =========="
echo "1. 创建包含Disabled键的测试plist..."
cat > /tmp/test_with_disabled.plist << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<false/>
	<key>KeepAlive</key>
	<true/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.session</string>
	<key>ProgramArguments</key>
	<array>
		<string>/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport</string>
		<string>t=runService</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist>
EOF

echo "✅ 包含Disabled键的测试plist已创建"

echo ""
echo "2. 验证plist格式..."
plutil -lint /tmp/test_with_disabled.plist

if [ $? -eq 0 ]; then
    echo "✅ plist格式正确"
else
    echo "❌ plist格式错误"
    exit 1
fi

echo ""
echo "3. 清理现有服务..."
sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist 2>/dev/null
sudo rm -f /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist

echo ""
echo "4. 安装包含Disabled键的测试plist..."
sudo cp /tmp/test_with_disabled.plist /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
sudo chmod 644 /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist

echo ""
echo "5. 尝试加载服务（包含Disabled键）..."
LOAD_RESULT_1=$(sudo launchctl load /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist 2>&1)

if [ $? -eq 0 ]; then
    echo "✅ 包含Disabled键的服务加载成功"
    if [ -n "$LOAD_RESULT_1" ]; then
        echo "加载输出: $LOAD_RESULT_1"
    fi
    DISABLED_SUCCESS=true
else
    echo "❌ 包含Disabled键的服务加载失败"
    echo "错误信息: $LOAD_RESULT_1"
    DISABLED_SUCCESS=false
fi

echo ""
echo "6. 检查服务状态（包含Disabled键）..."
SERVICE_STATUS_1=$(sudo launchctl list | grep remotesupport)
if [ -n "$SERVICE_STATUS_1" ]; then
    echo "✅ 服务已注册: $SERVICE_STATUS_1"
else
    echo "❌ 服务未注册"
fi

echo ""
echo "========== 测试2：不包含Disabled键 =========="
echo "7. 清理现有服务..."
sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist 2>/dev/null
sudo rm -f /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist

echo ""
echo "8. 创建不包含Disabled键的测试plist..."
cat > /tmp/test_without_disabled.plist << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>KeepAlive</key>
	<true/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.session</string>
	<key>ProgramArguments</key>
	<array>
		<string>/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport</string>
		<string>t=runService</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist>
EOF

echo "✅ 不包含Disabled键的测试plist已创建"

echo ""
echo "9. 验证plist格式..."
plutil -lint /tmp/test_without_disabled.plist

if [ $? -eq 0 ]; then
    echo "✅ plist格式正确"
else
    echo "❌ plist格式错误"
    exit 1
fi

echo ""
echo "10. 安装不包含Disabled键的测试plist..."
sudo cp /tmp/test_without_disabled.plist /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
sudo chmod 644 /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist

echo ""
echo "11. 尝试加载服务（不包含Disabled键）..."
LOAD_RESULT_2=$(sudo launchctl load /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist 2>&1)

if [ $? -eq 0 ]; then
    echo "✅ 不包含Disabled键的服务加载成功"
    if [ -n "$LOAD_RESULT_2" ]; then
        echo "加载输出: $LOAD_RESULT_2"
    fi
    NO_DISABLED_SUCCESS=true
else
    echo "❌ 不包含Disabled键的服务加载失败"
    echo "错误信息: $LOAD_RESULT_2"
    NO_DISABLED_SUCCESS=false
fi

echo ""
echo "12. 检查服务状态（不包含Disabled键）..."
SERVICE_STATUS_2=$(sudo launchctl list | grep remotesupport)
if [ -n "$SERVICE_STATUS_2" ]; then
    echo "✅ 服务已注册: $SERVICE_STATUS_2"
else
    echo "❌ 服务未注册"
fi

echo ""
echo "13. 等待3秒让服务启动..."
sleep 3

echo ""
echo "14. 检查进程状态..."
PROCESS_STATUS=$(ps aux | grep RemoteSupport | grep -v grep)
if [ -n "$PROCESS_STATUS" ]; then
    echo "✅ RemoteSupport进程正在运行:"
    echo "$PROCESS_STATUS"
else
    echo "❌ RemoteSupport进程未运行"
fi

echo ""
echo "========== 测试结论 =========="
echo "包含Disabled键的测试结果: $DISABLED_SUCCESS"
echo "不包含Disabled键的测试结果: $NO_DISABLED_SUCCESS"

if [ "$DISABLED_SUCCESS" = true ] && [ "$NO_DISABLED_SUCCESS" = true ]; then
    echo "🤔 结论：Disabled键不是问题，两种情况都能成功"
elif [ "$DISABLED_SUCCESS" = false ] && [ "$NO_DISABLED_SUCCESS" = true ]; then
    echo "🎯 结论：Disabled键是问题！移除Disabled键可以解决问题"
elif [ "$DISABLED_SUCCESS" = true ] && [ "$NO_DISABLED_SUCCESS" = false ]; then
    echo "🤔 结论：奇怪，包含Disabled键反而能成功？"
else
    echo "❌ 结论：两种情况都失败，问题可能在其他地方"
fi

echo ""
echo "测试结束时间: $(date)"
