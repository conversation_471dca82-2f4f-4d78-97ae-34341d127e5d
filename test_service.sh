#!/bin/bash

echo "=== macOS 10.12 RemoteSupport Service 测试 ==="
echo "测试时间: $(date)"
echo ""

# 创建测试plist
echo "1. 创建测试plist文件..."
cat > /tmp/test_session_manual.plist << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>KeepAlive</key>
	<true/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.session</string>
	<key>ProgramArguments</key>
	<array>
		<string>/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport</string>
		<string>t=runService</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist>
EOF

echo "✅ 测试plist文件已创建"

echo ""
echo "2. 验证plist格式..."
plutil -lint /tmp/test_session_manual.plist

if [ $? -eq 0 ]; then
    echo "✅ plist格式正确"
else
    echo "❌ plist格式错误，退出测试"
    exit 1
fi

echo ""
echo "3. 显示当前RemoteSupport进程状态..."
CURRENT_PROCESS=$(ps aux | grep RemoteSupport | grep -v grep)
if [ -n "$CURRENT_PROCESS" ]; then
    echo "当前运行的RemoteSupport进程:"
    echo "$CURRENT_PROCESS"
else
    echo "当前没有RemoteSupport进程运行"
fi

echo ""
echo "4. 清理现有服务..."
sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist 2>/dev/null
sudo rm -f /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
echo "✅ 现有服务已清理"

echo ""
echo "5. 安装测试plist..."
sudo cp /tmp/test_session_manual.plist /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
sudo chmod 644 /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
echo "✅ 测试plist已安装到 /Library/LaunchDaemons/"

echo ""
echo "6. 验证安装的plist文件..."
if [ -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist" ]; then
    echo "✅ plist文件存在"
    echo "文件大小: $(ls -la /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist | awk '{print $5}') bytes"
else
    echo "❌ plist文件不存在"
    exit 1
fi

echo ""
echo "7. 加载服务..."
LOAD_RESULT=$(sudo launchctl load /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist 2>&1)

if [ $? -eq 0 ]; then
    echo "✅ 服务加载成功"
    if [ -n "$LOAD_RESULT" ]; then
        echo "加载输出: $LOAD_RESULT"
    fi
else
    echo "❌ 服务加载失败"
    echo "错误信息: $LOAD_RESULT"
fi

echo ""
echo "8. 等待5秒让服务启动..."
sleep 5

echo ""
echo "9. 检查服务注册状态..."
SERVICE_STATUS=$(sudo launchctl list | grep remotesupport)
if [ -n "$SERVICE_STATUS" ]; then
    echo "✅ 服务已注册:"
    echo "$SERVICE_STATUS"
else
    echo "❌ 服务未注册"
fi

echo ""
echo "10. 检查进程状态..."
PROCESS_STATUS=$(ps aux | grep RemoteSupport | grep -v grep)
if [ -n "$PROCESS_STATUS" ]; then
    echo "✅ RemoteSupport进程正在运行:"
    echo "$PROCESS_STATUS"
else
    echo "❌ RemoteSupport进程未运行"
    echo ""
    echo "11. 尝试手动启动服务..."
    START_RESULT=$(sudo launchctl start com.sandstudio.remotesupport.session 2>&1)
    if [ $? -eq 0 ]; then
        echo "✅ 手动启动命令执行成功"
        if [ -n "$START_RESULT" ]; then
            echo "启动输出: $START_RESULT"
        fi
    else
        echo "❌ 手动启动命令失败"
        echo "错误信息: $START_RESULT"
    fi
    
    echo ""
    echo "等待3秒后再次检查进程..."
    sleep 3
    PROCESS_STATUS=$(ps aux | grep RemoteSupport | grep -v grep)
    if [ -n "$PROCESS_STATUS" ]; then
        echo "✅ 手动启动成功，进程正在运行:"
        echo "$PROCESS_STATUS"
    else
        echo "❌ 手动启动也失败，进程仍未运行"
    fi
fi

echo ""
echo "12. 最终状态检查..."
echo "服务列表中的remotesupport:"
sudo launchctl list | grep remotesupport || echo "未找到"

echo ""
echo "当前所有RemoteSupport相关进程:"
ps aux | grep -i remote | grep -v grep || echo "未找到"

echo ""
echo "=== 测试完成 ==="
echo "测试结束时间: $(date)"
