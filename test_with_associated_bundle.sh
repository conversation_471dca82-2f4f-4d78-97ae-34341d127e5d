#!/bin/bash

echo "=== 测试AssociatedBundleIdentifiers在macOS 10.12上的兼容性 ==="
echo "测试时间: $(date)"
echo ""

# 创建包含AssociatedBundleIdentifiers的测试plist（模仿ToDesk格式）
echo "1. 创建包含AssociatedBundleIdentifiers的测试plist..."
cat > /tmp/test_with_associated.plist << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>KeepAlive</key>
	<true/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.session</string>
	<key>ProgramArguments</key>
	<array>
		<string>/Applications/RemoteSupport.app/Contents/MacOS/RemoteSupport</string>
		<string>t=runService</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>/Applications/RemoteSupport.app/Contents/MacOS</string>
</dict>
</plist>
EOF

echo "✅ 包含AssociatedBundleIdentifiers的测试plist已创建"

echo ""
echo "2. 验证plist格式..."
plutil -lint /tmp/test_with_associated.plist

if [ $? -eq 0 ]; then
    echo "✅ plist格式正确"
else
    echo "❌ plist格式错误，退出测试"
    exit 1
fi

echo ""
echo "3. 清理现有服务..."
sudo launchctl unload /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist 2>/dev/null
sudo rm -f /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
echo "✅ 现有服务已清理"

echo ""
echo "4. 安装包含AssociatedBundleIdentifiers的测试plist..."
sudo cp /tmp/test_with_associated.plist /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
sudo chmod 644 /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
echo "✅ 测试plist已安装"

echo ""
echo "5. 尝试加载服务..."
LOAD_RESULT=$(sudo launchctl load /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist 2>&1)

if [ $? -eq 0 ]; then
    echo "✅ 服务加载成功！AssociatedBundleIdentifiers不是问题！"
    if [ -n "$LOAD_RESULT" ]; then
        echo "加载输出: $LOAD_RESULT"
    fi
else
    echo "❌ 服务加载失败，AssociatedBundleIdentifiers可能是问题"
    echo "错误信息: $LOAD_RESULT"
fi

echo ""
echo "6. 等待3秒让服务启动..."
sleep 3

echo ""
echo "7. 检查服务状态..."
SERVICE_STATUS=$(sudo launchctl list | grep remotesupport)
if [ -n "$SERVICE_STATUS" ]; then
    echo "✅ 服务已注册: $SERVICE_STATUS"
else
    echo "❌ 服务未注册"
fi

echo ""
echo "8. 检查进程状态..."
PROCESS_STATUS=$(ps aux | grep RemoteSupport | grep -v grep)
if [ -n "$PROCESS_STATUS" ]; then
    echo "✅ RemoteSupport进程正在运行:"
    echo "$PROCESS_STATUS"
else
    echo "❌ RemoteSupport进程未运行"
    echo ""
    echo "尝试手动启动服务..."
    START_RESULT=$(sudo launchctl start com.sandstudio.remotesupport.session 2>&1)
    if [ $? -eq 0 ]; then
        echo "✅ 手动启动命令执行成功"
        if [ -n "$START_RESULT" ]; then
            echo "启动输出: $START_RESULT"
        fi
        
        sleep 2
        PROCESS_STATUS=$(ps aux | grep RemoteSupport | grep -v grep)
        if [ -n "$PROCESS_STATUS" ]; then
            echo "✅ 手动启动成功，进程正在运行:"
            echo "$PROCESS_STATUS"
        else
            echo "❌ 手动启动也失败"
        fi
    else
        echo "❌ 手动启动命令失败"
        echo "错误信息: $START_RESULT"
    fi
fi

echo ""
echo "=== 测试结论 ==="
if [ -n "$SERVICE_STATUS" ] && [ -n "$PROCESS_STATUS" ]; then
    echo "🎉 结论：AssociatedBundleIdentifiers在macOS 10.12上是兼容的！"
    echo "    问题可能出在其他地方（如Disabled键或XML格式）"
else
    echo "❌ 结论：AssociatedBundleIdentifiers可能确实是问题所在"
fi

echo ""
echo "测试结束时间: $(date)"
