#!/bin/bash

echo "=== 查看现有的RemoteSupport plist文件 ==="
echo "查看时间: $(date)"
echo ""

echo "1. 列出所有RemoteSupport相关的plist文件..."
echo "LaunchDaemons目录:"
sudo ls -la /Library/LaunchDaemons/ | grep remotesupport

echo ""
echo "LaunchAgents目录:"
sudo ls -la /Library/LaunchAgents/ | grep remotesupport

echo ""
echo "2. 查看session plist内容..."
if sudo test -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"; then
    echo "=== Session plist 存在，内容如下 ==="
    sudo cat /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
    echo ""
    
    echo "=== Session plist 文件信息 ==="
    sudo ls -la /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
    sudo file /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist
    echo ""
else
    echo "❌ Session plist 不存在"
fi

echo ""
echo "3. 查看service plist内容..."
if sudo test -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist"; then
    echo "=== Service plist 存在，内容如下 ==="
    sudo cat /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist
    echo ""
    
    echo "=== Service plist 文件信息 ==="
    sudo ls -la /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist
    sudo file /Library/LaunchDaemons/com.sandstudio.remotesupport.service.plist
    echo ""
else
    echo "❌ Service plist 不存在"
fi

echo ""
echo "4. 验证plist格式..."
for plist in /Library/LaunchDaemons/com.sandstudio.remotesupport.*.plist; do
    if sudo test -f "$plist"; then
        echo "验证 $plist:"
        sudo plutil -lint "$plist"
        echo ""
    fi
done

echo ""
echo "5. 尝试加载session plist..."
if sudo test -f "/Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist"; then
    echo "尝试加载session服务..."
    LOAD_RESULT=$(sudo launchctl load /Library/LaunchDaemons/com.sandstudio.remotesupport.session.plist 2>&1)
    if [ $? -eq 0 ]; then
        echo "✅ Session plist加载成功"
        if [ -n "$LOAD_RESULT" ]; then
            echo "加载输出: $LOAD_RESULT"
        fi
    else
        echo "❌ Session plist加载失败"
        echo "错误信息: $LOAD_RESULT"
    fi
else
    echo "❌ 无法加载，session plist不存在"
fi

echo ""
echo "6. 检查当前服务状态..."
echo "当前注册的remotesupport服务:"
sudo launchctl list | grep remotesupport

echo ""
echo "当前运行的RemoteSupport进程:"
ps aux | grep RemoteSupport | grep -v grep

echo ""
echo "=== 查看完成 ==="
